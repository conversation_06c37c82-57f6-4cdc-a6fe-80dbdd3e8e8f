#!/usr/bin/env python3
"""
Check delivery personnel data in Firebase
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data

def check_delivery_personnel():
    """Check delivery personnel data in Firebase"""
    print("=== DELIVERY PERSONNEL DATA ===")
    
    # Get all delivery personnel data
    personnel = get_data("delivery_personnel") or {}
    print(f"Total personnel: {len(personnel)}")
    
    target_telegram_id = "1133538088"
    found_personnel = None
    
    for pid, pdata in personnel.items():
        print(f"\nPersonnel {pid}:")
        print(f"  Name: {pdata.get('name')}")
        print(f"  Telegram ID: {pdata.get('telegram_id')}")
        print(f"  Status: {pdata.get('status')}")
        print(f"  Verified: {pdata.get('is_verified')}")
        print(f"  Service Areas: {pdata.get('service_areas')}")
        print(f"  Max Capacity: {pdata.get('max_capacity')}")
        print(f"  Vehicle Type: {pdata.get('vehicle_type')}")
        
        if pdata.get('telegram_id') == target_telegram_id:
            found_personnel = pid
    
    print("\n=== AVAILABILITY DATA ===")
    availability = get_data("delivery_personnel_availability") or {}
    for pid, status in availability.items():
        print(f"{pid}: {status}")
    
    print("\n=== CAPACITY DATA ===")
    capacity = get_data("delivery_personnel_capacity") or {}
    for pid, cap in capacity.items():
        print(f"{pid}: {cap}")
    
    print("\n=== ZONES DATA ===")
    zones = get_data("delivery_personnel_zones") or {}
    for pid, zone_data in zones.items():
        print(f"{pid}: {zone_data}")
    
    # Check specific personnel
    print(f"\n=== TARGET PERSONNEL CHECK (Telegram ID: {target_telegram_id}) ===")
    if found_personnel:
        print(f"✅ Found personnel: {found_personnel}")
        
        # Check all related data
        personnel_data = personnel.get(found_personnel, {})
        availability_status = availability.get(found_personnel, "NOT_SET")
        capacity_count = capacity.get(found_personnel, "NOT_SET")
        zone_assignments = zones.get(found_personnel, "NOT_SET")
        
        print(f"Personnel Data: {personnel_data}")
        print(f"Availability: {availability_status}")
        print(f"Capacity: {capacity_count}")
        print(f"Zone Assignments: {zone_assignments}")
        
        # Check if personnel is available for delivery
        is_available = (
            personnel_data.get('status') in ['available', 'online'] and
            personnel_data.get('is_verified') == True and
            availability_status == 'available' and
            isinstance(capacity_count, int) and capacity_count < personnel_data.get('max_capacity', 5)
        )
        
        print(f"\n🎯 AVAILABILITY CHECK:")
        print(f"  Status OK: {personnel_data.get('status') in ['available', 'online']}")
        print(f"  Verified: {personnel_data.get('is_verified') == True}")
        print(f"  Available: {availability_status == 'available'}")
        print(f"  Capacity OK: {isinstance(capacity_count, int) and capacity_count < personnel_data.get('max_capacity', 5)}")
        print(f"  Overall Available: {is_available}")
        
    else:
        print(f"❌ Personnel with Telegram ID {target_telegram_id} NOT FOUND")
        print("Available Telegram IDs:")
        for pid, pdata in personnel.items():
            print(f"  {pid}: {pdata.get('telegram_id')}")

if __name__ == "__main__":
    check_delivery_personnel()
