#!/usr/bin/env python3
"""
Comprehensive test for new delivery personnel and complete order workflow
"""

import sys
import os
import time
import uuid
import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_delivery_personnel_to_firebase():
    """Add delivery personnel directly to Firebase"""
    print("🚚 ADDING NEW DELIVERY PERSONNEL TO FIREBASE")
    print("=" * 60)
    
    try:
        # Import Firebase functions
        from src.firebase_db import set_data, get_data, update_data
        
        # Check if personnel already exists
        personnel_data = get_data("delivery_personnel") or {}
        telegram_id = "1133538088"
        
        # Check if already exists
        for pid, pdata in personnel_data.items():
            if pdata.get('telegram_id') == telegram_id:
                print(f"✅ Personnel with Telegram ID {telegram_id} already exists: {pid}")
                return pid
        
        # Generate new personnel ID
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"
        
        # Create personnel record
        new_personnel = {
            "personnel_id": personnel_id,
            "name": "New Delivery Personnel",
            "phone_number": "+************",
            "telegram_id": telegram_id,
            "email": "<EMAIL>",
            "service_areas": ["1", "2", "3", "4"],
            "max_capacity": 5,
            "current_capacity": 0,
            "status": "offline",
            "created_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "last_active": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "is_verified": True,
            "emergency_contact": None,
            "vehicle_type": "motorcycle",
            "rating": 5.0,
            "total_deliveries": 0,
            "successful_deliveries": 0
        }
        
        # Update Firebase with new personnel
        if update_data(f"delivery_personnel/{personnel_id}", new_personnel):
            print(f"✅ Added personnel to Firebase: {personnel_id}")
        else:
            print("❌ Failed to add personnel to Firebase")
            return None
        
        # Add to availability
        if update_data(f"delivery_personnel_availability/{personnel_id}", "offline"):
            print(f"✅ Added availability status")
        
        # Add to capacity
        if update_data(f"delivery_personnel_capacity/{personnel_id}", 0):
            print(f"✅ Added capacity data")
        
        # Add to zones
        if update_data(f"delivery_personnel_zones/{personnel_id}", ["1", "2", "3", "4"]):
            print(f"✅ Added zones data")
        
        # Add to performance
        performance_data = {
            "total_deliveries": 0,
            "successful_deliveries": 0,
            "average_rating": 5.0,
            "total_distance": 0.0,
            "average_delivery_time": 0.0,
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        if update_data(f"delivery_personnel_performance/{personnel_id}", performance_data):
            print(f"✅ Added performance data")
        
        print(f"🎉 Successfully added delivery personnel: {personnel_id}")
        return personnel_id
        
    except Exception as e:
        print(f"❌ Error adding personnel: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_personnel_integration():
    """Verify the personnel is properly integrated"""
    print("\n🔍 VERIFYING PERSONNEL INTEGRATION")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data
        
        # Check all delivery personnel data structures
        personnel_data = get_data("delivery_personnel") or {}
        availability_data = get_data("delivery_personnel_availability") or {}
        capacity_data = get_data("delivery_personnel_capacity") or {}
        zones_data = get_data("delivery_personnel_zones") or {}
        performance_data = get_data("delivery_personnel_performance") or {}
        
        telegram_id = "1133538088"
        found_personnel = None
        
        for pid, pdata in personnel_data.items():
            if pdata.get('telegram_id') == telegram_id:
                found_personnel = (pid, pdata)
                break
        
        if found_personnel:
            pid, pdata = found_personnel
            print(f"✅ Personnel found in Firebase:")
            print(f"   Personnel ID: {pid}")
            print(f"   Name: {pdata.get('name')}")
            print(f"   Telegram ID: {pdata.get('telegram_id')}")
            print(f"   Service Areas: {pdata.get('service_areas')}")
            print(f"   Max Capacity: {pdata.get('max_capacity')}")
            print(f"   Verified: {pdata.get('is_verified')}")
            
            # Check all data structures
            checks = [
                ("Availability", pid in availability_data),
                ("Capacity", pid in capacity_data),
                ("Zones", pid in zones_data),
                ("Performance", pid in performance_data)
            ]
            
            all_good = True
            for check_name, check_result in checks:
                if check_result:
                    print(f"   ✅ {check_name}: OK")
                else:
                    print(f"   ❌ {check_name}: MISSING")
                    all_good = False
            
            return all_good
        else:
            print(f"❌ Personnel with Telegram ID {telegram_id} not found")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying integration: {e}")
        return False

def test_delivery_bot_recognition():
    """Test if delivery bot recognizes the new personnel"""
    print("\n🤖 TESTING DELIVERY BOT RECOGNITION")
    print("=" * 60)
    
    try:
        # Import delivery bot functions
        from src.bots.delivery_bot import get_personnel_by_telegram_id
        
        telegram_id = 1133538088  # As integer
        personnel = get_personnel_by_telegram_id(telegram_id)
        
        if personnel:
            print(f"✅ Delivery bot recognizes personnel:")
            print(f"   Personnel ID: {personnel.personnel_id}")
            print(f"   Name: {personnel.name}")
            print(f"   Telegram ID: {personnel.telegram_id}")
            print(f"   Service Areas: {personnel.service_areas}")
            print(f"   Verified: {personnel.is_verified}")
            return True
        else:
            print(f"❌ Delivery bot does NOT recognize Telegram ID {telegram_id}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery bot: {e}")
        return False

def test_broadcast_system():
    """Test if the new personnel will receive broadcasts"""
    print("\n📡 TESTING BROADCAST SYSTEM INTEGRATION")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        # Test availability for different areas
        test_areas = ["1", "2", "3", "4"]
        
        for area_id in test_areas:
            available_personnel = find_available_personnel(area_id)
            
            # Check if our new personnel is in the list
            telegram_id = "1133538088"
            found_in_area = False
            
            for personnel in available_personnel:
                if hasattr(personnel, 'telegram_id') and personnel.telegram_id == telegram_id:
                    found_in_area = True
                    break
            
            if found_in_area:
                print(f"   ✅ Area {area_id}: Personnel will receive broadcasts")
            else:
                print(f"   ⚠️  Area {area_id}: Personnel may not receive broadcasts")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing broadcast system: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 COMPREHENSIVE DELIVERY PERSONNEL AND WORKFLOW TEST")
    print("=" * 80)
    
    # Step 1: Add delivery personnel
    personnel_id = add_delivery_personnel_to_firebase()
    
    if not personnel_id:
        print("❌ Failed to add delivery personnel. Cannot continue.")
        return False
    
    # Step 2: Verify integration
    integration_ok = verify_personnel_integration()
    
    # Step 3: Test delivery bot recognition
    bot_recognition_ok = test_delivery_bot_recognition()
    
    # Step 4: Test broadcast system
    broadcast_ok = test_broadcast_system()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY:")
    print(f"   Personnel Addition: {'✅ Success' if personnel_id else '❌ Failed'}")
    print(f"   Integration Check: {'✅ Success' if integration_ok else '❌ Failed'}")
    print(f"   Bot Recognition: {'✅ Success' if bot_recognition_ok else '❌ Failed'}")
    print(f"   Broadcast System: {'✅ Success' if broadcast_ok else '❌ Failed'}")
    
    if personnel_id and integration_ok and bot_recognition_ok and broadcast_ok:
        print("\n🎉 ALL TESTS PASSED!")
        print(f"   New delivery personnel {personnel_id} is ready for service")
        print(f"   Telegram ID 1133538088 will receive order notifications")
        print(f"   Complete Order workflow is ready for testing")
        
        print("\n📋 NEXT STEPS FOR MANUAL TESTING:")
        print("1. Place a test order through the user bot")
        print("2. Approve through admin and finance bots")
        print("3. Verify both existing and new delivery personnel receive notifications")
        print("4. Accept order with new personnel (Telegram ID 1133538088)")
        print("5. Test 'Complete Order' button functionality")
        print("6. Verify customer confirmation workflow")
        
        return True
    else:
        print("\n⚠️  Some tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
