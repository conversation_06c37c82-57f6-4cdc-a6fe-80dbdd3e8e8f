#!/usr/bin/env python3
"""
Test Management Bot Response
Tests if the management bot responds to commands properly
"""

import sys
import os
import time
import threading

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_management_bot():
    """Test management bot functionality"""
    print("🧪 TESTING MANAGEMENT BOT RESPONSE")
    print("=" * 50)
    
    try:
        # Import management bot components
        from src.bots.management_bot import management_bot, register_management_bot_handlers, is_authorized_user
        from src.config import logger
        
        print("✅ Management bot imported successfully")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        # Test authorization function
        test_user_id = 7729984017
        is_authorized = is_authorized_user(test_user_id)
        print(f"✅ Authorization test for {test_user_id}: {is_authorized}")
        
        # Register handlers
        register_management_bot_handlers()
        print("✅ Handlers registered successfully")
        
        # Check handler counts
        message_handlers = len(management_bot.message_handlers)
        callback_handlers = len(management_bot.callback_query_handlers)
        print(f"✅ Message handlers: {message_handlers}")
        print(f"✅ Callback handlers: {callback_handlers}")
        
        # Test polling for a short time
        print("\n🚀 Starting bot polling test (10 seconds)...")
        print("Send /start command to @Wiz_Aroma_Finance_bot now!")
        
        def polling_test():
            try:
                management_bot.polling(timeout=10, none_stop=False)
            except Exception as e:
                print(f"Polling stopped: {e}")
        
        # Start polling in a separate thread
        polling_thread = threading.Thread(target=polling_test)
        polling_thread.daemon = True
        polling_thread.start()
        
        # Wait for 10 seconds
        time.sleep(10)
        
        print("\n✅ Polling test completed")
        print("\nIf you sent /start command and received a response, the bot is working!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing management bot: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_management_bot()
    if success:
        print("\n🎉 Management bot test completed successfully!")
    else:
        print("\n💥 Management bot test failed!")
        sys.exit(1)
