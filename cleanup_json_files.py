#!/usr/bin/env python
"""
Cleanup script to delete all legacy JSON data files after migration to Firebase.
"""

import os
import shutil
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("cleanup")

# Path to data directory
DATA_DIR = "data_files"


def delete_json_files():
    """Delete all JSON files and their backups from the data_files directory"""
    try:
        if os.path.exists(DATA_DIR):
            # Get list of all JSON files
            json_files = [
                f
                for f in os.listdir(DATA_DIR)
                if f.endswith(".json") or f.endswith(".json.backup")
            ]

            # Log files to be deleted
            logger.info(f"Found {len(json_files)} JSON files to delete:")
            for f in json_files:
                logger.info(f"  - {f}")

            # Delete each file
            for filename in json_files:
                file_path = os.path.join(DATA_DIR, filename)
                try:
                    os.remove(file_path)
                    logger.info(f"Deleted {file_path}")
                except Exception as e:
                    logger.error(f"Error deleting {file_path}: {str(e)}")

            # Check if directory is empty
            remaining_files = os.listdir(DATA_DIR)
            if not remaining_files:
                # Directory is empty, delete it
                shutil.rmtree(DATA_DIR)
                logger.info(f"Deleted empty directory {DATA_DIR}")
            else:
                logger.info(
                    f"Directory {DATA_DIR} still contains {len(remaining_files)} non-JSON files"
                )

            logger.info("Cleanup completed successfully")
        else:
            logger.info(f"Directory {DATA_DIR} does not exist, nothing to clean up")
    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")


if __name__ == "__main__":
    logger.info("Starting JSON files cleanup after Firebase migration")
    delete_json_files()
