#!/usr/bin/env python3
"""
Final test for Order Not Received fix
"""

import sys
sys.path.insert(0, 'src')

def test_import_fix():
    """Test if the import issue is fixed"""
    print("🔍 Testing import fix for get_restaurant_by_id...")
    
    try:
        # Test importing the function that was causing the error
        from src.data_storage import get_restaurant_by_id
        print("✅ Successfully imported get_restaurant_by_id from src.data_storage")
        
        # Test the function with a sample restaurant ID
        restaurant = get_restaurant_by_id(1)
        if restaurant:
            print(f"✅ Function works! Found restaurant: {restaurant.get('name', 'Unknown')}")
        else:
            print("⚠️ Function works but no restaurant found with ID 1")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Function error: {e}")
        return False

def test_callback_handler_import():
    """Test if the callback handler can be imported without errors"""
    print("\n🔍 Testing callback handler import...")
    
    try:
        from src.handlers.order_handlers import handle_order_not_received
        print("✅ Successfully imported handle_order_not_received")
        
        # Check if it's callable
        if callable(handle_order_not_received):
            print("✅ Function is callable")
        else:
            print("❌ Function is not callable")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_delivery_personnel_function():
    """Test delivery personnel function"""
    print("\n🔍 Testing delivery personnel function...")
    
    try:
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
        print("✅ Successfully imported get_delivery_personnel_by_id")
        
        # Test with a known personnel ID from the logs
        personnel = get_delivery_personnel_by_id("dp_19a497f8")
        if personnel:
            print(f"✅ Found personnel: {personnel.get('name', 'Unknown')}")
        else:
            print("⚠️ No personnel found with ID dp_19a497f8")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_complete_order_not_received_flow():
    """Test the complete Order Not Received flow"""
    print("\n🔍 Testing complete Order Not Received flow...")
    
    try:
        from src.firebase_db import get_data
        from src.data_storage import get_restaurant_by_id
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
        
        # Get confirmed orders
        confirmed_orders = get_data("confirmed_orders")
        if not confirmed_orders:
            print("⚠️ No confirmed orders found for testing")
            return True
            
        # Find a completed order to test with
        completed_orders = []
        for order_id, order_data in confirmed_orders.items():
            if order_data.get('delivery_status') == 'completed':
                completed_orders.append((order_id, order_data))
        
        if not completed_orders:
            print("⚠️ No completed orders found for testing")
            return True
            
        # Test with the first completed order
        test_order_id, test_order = completed_orders[0]
        print(f"📄 Testing with order: {test_order_id}")
        
        # Test restaurant lookup
        restaurant_id = test_order.get('restaurant_id')
        if restaurant_id:
            restaurant = get_restaurant_by_id(restaurant_id)
            if restaurant:
                print(f"✅ Restaurant lookup successful: {restaurant.get('name', 'Unknown')}")
            else:
                print(f"⚠️ Restaurant not found for ID: {restaurant_id}")
        else:
            print("⚠️ No restaurant_id in order data")
            
        # Test delivery personnel lookup
        assigned_to = test_order.get('assigned_to')
        if assigned_to:
            personnel = get_delivery_personnel_by_id(assigned_to)
            if personnel:
                print(f"✅ Personnel lookup successful: {personnel.get('name', 'Unknown')}")
                print(f"   - Telegram ID: {personnel.get('telegram_id', 'N/A')}")
            else:
                print(f"⚠️ Personnel not found for ID: {assigned_to}")
        else:
            print("⚠️ No assigned_to in order data")
            
        print("✅ All components for Order Not Received flow are working")
        return True
        
    except Exception as e:
        print(f"❌ Error testing complete flow: {e}")
        return False

def test_delivery_bot_connection():
    """Test delivery bot connection"""
    print("\n🔍 Testing delivery bot connection...")
    
    try:
        from src.bots.delivery_bot import delivery_bot
        print("✅ Successfully imported delivery_bot")
        
        # Test bot info (this will verify the token is valid)
        bot_info = delivery_bot.get_me()
        print(f"✅ Delivery bot connected: @{bot_info.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error connecting to delivery bot: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Final Test for Order Not Received Fix...\n")
    
    tests = [
        ("Import Fix", test_import_fix),
        ("Callback Handler Import", test_callback_handler_import),
        ("Delivery Personnel Function", test_delivery_personnel_function),
        ("Delivery Bot Connection", test_delivery_bot_connection),
        ("Complete Order Not Received Flow", test_complete_order_not_received_flow),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*60)
    print("📊 FINAL ORDER NOT RECEIVED FIX TEST RESULTS")
    print("="*60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ The Order Not Received fix is working correctly")
        print("💡 The import error has been resolved")
        print("🔧 All components are functioning properly")
        print("\n📋 Next Steps:")
        print("   1. Test with actual order by clicking 'Order Not Received' button")
        print("   2. Verify delivery personnel receive notifications")
        print("   3. Check that order status is updated correctly")
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
