#!/usr/bin/env python3
"""
Test script to verify privacy controls and data accuracy in delivery bot and order tracking bot notifications.
"""

import sys
import os
import datetime

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_message_formatting():
    """Test the message formatting functions for both bots"""
    print("🧪 Testing Message Formatting Functions")
    print("=" * 50)
    
    try:
        # Import the formatting functions
        from src.handlers.payment_handlers import (
            format_delivery_bot_message,
            format_order_tracking_bot_message,
            format_delivery_location,
            get_restaurant_phone,
            get_proper_restaurant_name,
            get_proper_pickup_location
        )
        
        print("✅ Successfully imported formatting functions")
        
        # Create test order data
        test_order_data = {
            'restaurant_id': '1',
            'phone_number': '+251912345678',
            'delivery_name': '<PERSON>',
            'delivery_location': 'B-371',
            'delivery_gate': 'Fresh',
            'area_name': 'Downtown',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-01-15 14:30:00',
            'items': [
                {'name': 'Pizza Margherita', 'price': 120, 'quantity': 1},
                {'name': 'Coca Cola', 'price': 30, 'quantity': 1}
            ]
        }
        
        # Format items text
        items_text = "📋 **Order Items:**\n"
        for item in test_order_data['items']:
            items_text += f"• {item['name']} x{item['quantity']} - {item['price'] * item['quantity']} Birr\n"
        items_text += "\n"
        
        test_order_number = "TEST_001"
        test_restaurant_name = "Pizza Palace"
        test_approved_at = "2024-01-15 14:35:00"
        
        print("\n📱 DELIVERY BOT MESSAGE (Privacy Protected):")
        print("-" * 40)
        delivery_message = format_delivery_bot_message(
            test_order_number,
            test_restaurant_name,
            test_order_data,
            items_text,
            test_approved_at
        )
        print(delivery_message)
        
        print("\n🖥️ ORDER TRACKING BOT MESSAGE (Full Administrative Details):")
        print("-" * 40)
        tracking_message = format_order_tracking_bot_message(
            test_order_number,
            test_restaurant_name,
            test_order_data,
            items_text,
            test_approved_at
        )
        print(tracking_message)
        
        # Verify privacy controls
        print("\n🔒 PRIVACY VERIFICATION:")
        print("-" * 30)
        
        # Check delivery bot privacy
        restaurant_phone = get_restaurant_phone()
        if restaurant_phone in delivery_message:
            print(f"✅ Delivery bot uses restaurant phone: {restaurant_phone}")
        else:
            print(f"❌ Delivery bot missing restaurant phone")
            
        if test_order_data['phone_number'] not in delivery_message:
            print("✅ Delivery bot hides customer phone number")
        else:
            print("❌ Delivery bot exposes customer phone number")
            
        if test_order_data['delivery_name'] not in delivery_message:
            print("✅ Delivery bot hides customer name")
        else:
            print("❌ Delivery bot exposes customer name")
            
        if "Delivery Fee" not in delivery_message:
            print("✅ Delivery bot hides delivery fee")
        else:
            print("❌ Delivery bot shows delivery fee")
            
        # Check order tracking bot completeness
        if test_order_data['phone_number'] in tracking_message:
            print("✅ Order tracking bot shows customer phone")
        else:
            print("❌ Order tracking bot missing customer phone")
            
        if test_order_data['delivery_name'] in tracking_message:
            print("✅ Order tracking bot shows customer name")
        else:
            print("❌ Order tracking bot missing customer name")
            
        if "Delivery Fee" in tracking_message:
            print("✅ Order tracking bot shows delivery fee")
        else:
            print("❌ Order tracking bot missing delivery fee")
            
        # Check location formatting
        print("\n📍 LOCATION FORMATTING VERIFICATION:")
        print("-" * 35)
        
        formatted_location = format_delivery_location(
            test_order_data['delivery_location'],
            test_order_data['delivery_gate']
        )
        expected_location = "B-371 (Fresh)"
        
        if formatted_location == expected_location:
            print(f"✅ Location formatting correct: {formatted_location}")
        else:
            print(f"❌ Location formatting incorrect: {formatted_location} (expected: {expected_location})")
            
        # Test N/A handling
        na_location = format_delivery_location('N/A', 'N/A')
        if na_location == 'Location not specified':
            print("✅ N/A values handled correctly")
        else:
            print(f"❌ N/A values not handled correctly: {na_location}")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing message formatting: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_bot_functions():
    """Test delivery bot specific formatting functions"""
    print("\n🚚 Testing Delivery Bot Functions")
    print("=" * 40)
    
    try:
        from src.bots.delivery_bot import (
            format_delivery_location_for_bot,
            get_restaurant_phone_for_delivery,
            format_privacy_protected_order_info,
            get_proper_restaurant_name_for_bot
        )
        
        print("✅ Successfully imported delivery bot functions")
        
        # Test data
        test_order_data = {
            'restaurant_id': '1',
            'delivery_location': 'B-371',
            'delivery_gate': 'Fresh',
            'subtotal': 150
        }
        
        items_text = "• Pizza Margherita - 120 birr\n• Coca Cola - 30 birr\n"
        
        # Test privacy protected order info
        order_info = format_privacy_protected_order_info(
            "TEST_002",
            test_order_data,
            "Pizza Palace",
            items_text
        )
        
        print("\n📋 Privacy Protected Order Info:")
        print(order_info)
        
        # Verify privacy controls
        restaurant_phone = get_restaurant_phone_for_delivery()
        if restaurant_phone in order_info and restaurant_phone == "0909782606":
            print("✅ Uses correct restaurant phone number")
        else:
            print("❌ Incorrect restaurant phone number")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing delivery bot functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔒 PRIVACY CONTROLS AND DATA ACCURACY TEST")
    print("=" * 60)
    print("Testing delivery bot privacy controls and order tracking bot data accuracy")
    print()
    
    success = True
    
    # Test message formatting
    if not test_message_formatting():
        success = False
        
    # Test delivery bot functions
    if not test_delivery_bot_functions():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("✅ ALL TESTS PASSED - Privacy controls and data accuracy verified!")
        print("\n📋 Summary:")
        print("• Delivery bot hides customer personal information")
        print("• Delivery bot uses restaurant phone (0909782606)")
        print("• Delivery bot hides delivery fees")
        print("• Order tracking bot shows complete administrative details")
        print("• Location formatting handles N/A values properly")
        print("• Restaurant names are properly retrieved")
    else:
        print("❌ SOME TESTS FAILED - Please review the implementation")
    
    return success

if __name__ == "__main__":
    main()
