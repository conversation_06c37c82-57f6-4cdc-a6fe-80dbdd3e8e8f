#!/usr/bin/env python3
"""
Simple script to check Firebase configuration and data storage settings.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def check_firebase_config():
    """Check Firebase configuration"""
    print("🔥 Checking Firebase Configuration...")
    
    try:
        # Check USE_FIREBASE flag
        from src.data_storage import USE_FIREBASE
        print(f"USE_FIREBASE flag: {USE_FIREBASE}")
        
        # Check Firebase initialization
        from src.firebase_db import initialize_firebase
        print(f"Firebase initialization function available: {callable(initialize_firebase)}")
        
        # Check Firebase operations
        from src.firebase_db import get_data, set_data, update_data, delete_data
        print(f"Firebase operations available:")
        print(f"  - get_data: {callable(get_data)}")
        print(f"  - set_data: {callable(set_data)}")
        print(f"  - update_data: {callable(update_data)}")
        print(f"  - delete_data: {callable(delete_data)}")
        
        return True
    except Exception as e:
        print(f"Error checking Firebase config: {e}")
        return False

def check_data_storage_functions():
    """Check data storage functions"""
    print("\n📊 Checking Data Storage Functions...")
    
    try:
        # Check load_user_data function
        from src.data_storage import load_user_data
        print(f"load_user_data function available: {callable(load_user_data)}")
        
        # Check delivery personnel data functions
        from src.data_storage import (
            load_delivery_personnel_data,
            load_delivery_personnel_assignments_data,
            load_delivery_personnel_availability_data
        )
        print(f"Delivery personnel data functions available:")
        print(f"  - load_delivery_personnel_data: {callable(load_delivery_personnel_data)}")
        print(f"  - load_delivery_personnel_assignments_data: {callable(load_delivery_personnel_assignments_data)}")
        print(f"  - load_delivery_personnel_availability_data: {callable(load_delivery_personnel_availability_data)}")
        
        return True
    except Exception as e:
        print(f"Error checking data storage functions: {e}")
        return False

def check_data_models():
    """Check data models"""
    print("\n📋 Checking Data Models...")
    
    try:
        # Check delivery personnel data models
        from src.data_models import (
            delivery_personnel,
            delivery_personnel_availability,
            delivery_personnel_capacity,
            delivery_personnel_assignments
        )
        print(f"Delivery personnel data models available:")
        print(f"  - delivery_personnel: {type(delivery_personnel)}")
        print(f"  - delivery_personnel_availability: {type(delivery_personnel_availability)}")
        print(f"  - delivery_personnel_capacity: {type(delivery_personnel_capacity)}")
        print(f"  - delivery_personnel_assignments: {type(delivery_personnel_assignments)}")
        
        return True
    except Exception as e:
        print(f"Error checking data models: {e}")
        return False

def main():
    """Run all checks"""
    print("🚀 Firebase Configuration Check")
    print("=" * 50)
    
    firebase_config = check_firebase_config()
    data_storage = check_data_storage_functions()
    data_models = check_data_models()
    
    print("\n" + "=" * 50)
    print("📊 SUMMARY")
    print("=" * 50)
    print(f"Firebase Configuration: {'✅ PASSED' if firebase_config else '❌ FAILED'}")
    print(f"Data Storage Functions: {'✅ PASSED' if data_storage else '❌ FAILED'}")
    print(f"Data Models: {'✅ PASSED' if data_models else '❌ FAILED'}")
    
    all_passed = firebase_config and data_storage and data_models
    print(f"\nOverall: {'✅ PASSED' if all_passed else '❌ FAILED'}")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
