#!/usr/bin/env python3
"""
Fix Firebase path issues in the Wiz-Aroma codebase.
This script removes leading slashes from Firebase database reference paths.
"""

import os
import re
import sys

def fix_firebase_paths_in_file(file_path):
    """Fix Firebase paths in a single file"""
    print(f"Processing: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # Pattern 1: get_data(f"/path/{variable}")
        pattern1 = r'get_data\(f"(/[^"]+)"\)'
        def replace1(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'get_data(f"{path[1:]}")'
            return match.group(0)
        
        content, count1 = re.subn(pattern1, replace1, content)
        changes_made += count1
        
        # Pattern 2: get_data("/path")
        pattern2 = r'get_data\("(/[^"]+)"\)'
        def replace2(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'get_data("{path[1:]}")'
            return match.group(0)
        
        content, count2 = re.subn(pattern2, replace2, content)
        changes_made += count2
        
        # Pattern 3: set_data(f"/path/{variable}", ...)
        pattern3 = r'set_data\(f"(/[^"]+)",'
        def replace3(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'set_data(f"{path[1:]}",'
            return match.group(0)
        
        content, count3 = re.subn(pattern3, replace3, content)
        changes_made += count3
        
        # Pattern 4: set_data("/path", ...)
        pattern4 = r'set_data\("(/[^"]+)",'
        def replace4(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'set_data("{path[1:]}",'
            return match.group(0)
        
        content, count4 = re.subn(pattern4, replace4, content)
        changes_made += count4
        
        # Pattern 5: update_data("/path", ...)
        pattern5 = r'update_data\("(/[^"]+)",'
        def replace5(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'update_data("{path[1:]}",'
            return match.group(0)
        
        content, count5 = re.subn(pattern5, replace5, content)
        changes_made += count5
        
        # Pattern 6: delete_data(f"/path/{variable}")
        pattern6 = r'delete_data\(f"(/[^"]+)"\)'
        def replace6(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'delete_data(f"{path[1:]}")'
            return match.group(0)
        
        content, count6 = re.subn(pattern6, replace6, content)
        changes_made += count6
        
        # Pattern 7: delete_data("/path")
        pattern7 = r'delete_data\("(/[^"]+)"\)'
        def replace7(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'delete_data("{path[1:]}")'
            return match.group(0)
        
        content, count7 = re.subn(pattern7, replace7, content)
        changes_made += count7
        
        # Pattern 8: db.reference("/path")
        pattern8 = r'db\.reference\("(/[^"]+)"\)'
        def replace8(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'db.reference("{path[1:]}")'
            return match.group(0)
        
        content, count8 = re.subn(pattern8, replace8, content)
        changes_made += count8
        
        # Pattern 9: db.reference(f"/path/{variable}")
        pattern9 = r'db\.reference\(f"(/[^"]+)"\)'
        def replace9(match):
            path = match.group(1)
            if path.startswith('/'):
                return f'db.reference(f"{path[1:]}")'
            return match.group(0)
        
        content, count9 = re.subn(pattern9, replace9, content)
        changes_made += count9
        
        if changes_made > 0:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"  ✅ Fixed {changes_made} Firebase path issues")
            return True
        else:
            print(f"  ℹ️  No Firebase path issues found")
            return False
            
    except Exception as e:
        print(f"  ❌ Error processing {file_path}: {e}")
        return False

def main():
    """Main function to fix Firebase paths in all relevant files"""
    print("🔧 Firebase Path Fixer for Wiz-Aroma V-1.3.3")
    print("=" * 50)
    
    # Files to process
    files_to_process = [
        "src/firebase_db.py",
        "src/handlers/admin_handlers.py",
        "src/data_storage.py",
        "src/migrate_to_firebase.py"
    ]
    
    total_files_fixed = 0
    
    for file_path in files_to_process:
        if os.path.exists(file_path):
            if fix_firebase_paths_in_file(file_path):
                total_files_fixed += 1
        else:
            print(f"⚠️  File not found: {file_path}")
    
    print("\n" + "=" * 50)
    print(f"🎉 Fixed Firebase paths in {total_files_fixed} files")
    print("✅ All Firebase paths should now work correctly!")

if __name__ == "__main__":
    main()
