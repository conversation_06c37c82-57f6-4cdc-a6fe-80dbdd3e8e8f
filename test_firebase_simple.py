#!/usr/bin/env python3
"""
Simple Firebase connectivity test for Wiz-Aroma V-1.3.3
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append('src')

try:
    from firebase_db import initialize_firebase, test_firebase_connectivity
    
    print("🔧 Simple Firebase Connectivity Test")
    print("=" * 40)
    
    # Test Firebase initialization
    print("1. Testing Firebase initialization...")
    try:
        initialize_firebase()
        print("   ✅ Firebase initialized successfully")
    except Exception as e:
        print(f"   ❌ Firebase initialization failed: {e}")
        sys.exit(1)
    
    # Test Firebase connectivity
    print("2. Testing Firebase connectivity...")
    try:
        result = test_firebase_connectivity()
        if result:
            print("   ✅ Firebase connectivity test passed")
        else:
            print("   ❌ Firebase connectivity test failed")
    except Exception as e:
        print(f"   ❌ Firebase connectivity error: {e}")
    
    print("\n" + "=" * 40)
    print("🎉 Firebase test completed!")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    sys.exit(1)
