#!/usr/bin/env python3
"""
Comprehensive verification of the authorization fix
Tests the complete workflow for user 5546595738
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_firebase_authorization():
    """Test Firebase authorization data"""
    print("🔍 Test 1: Firebase Authorization Data")
    try:
        from src.firebase_db import get_data
        
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        print(f"📊 Total authorized personnel: {len(authorized_personnel)}")
        
        # Check for user 5546595738
        target_user = 5546595738
        user_found = False
        
        for personnel_id, person_data in authorized_personnel.items():
            telegram_id = person_data.get('telegram_id')
            if telegram_id == target_user:
                user_found = True
                print(f"✅ Target user {target_user} found:")
                print(f"   Personnel ID: {personnel_id}")
                print(f"   Name: {person_data.get('name', 'Unknown')}")
                print(f"   Status: {person_data.get('status', 'unknown')}")
                print(f"   Added: {person_data.get('added_date', 'unknown')}")
                break
        
        if not user_found:
            print(f"❌ Target user {target_user} not found in Firebase")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Firebase test failed: {e}")
        return False

def test_delivery_bot_authorization():
    """Test delivery bot authorization functions"""
    print("\n🔍 Test 2: Delivery Bot Authorization")
    try:
        from src.bots.delivery_bot import (
            get_authorized_delivery_ids_from_firebase,
            is_authorized,
            clear_authorization_cache
        )
        
        target_user = 5546595738
        
        # Clear cache first
        print("🔄 Clearing authorization cache...")
        clear_authorization_cache()
        
        # Get authorized IDs
        print("📋 Getting authorized IDs from Firebase...")
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        print(f"✅ Retrieved authorized IDs: {authorized_ids}")
        
        # Check if target user is in the list
        if target_user in authorized_ids:
            print(f"✅ Target user {target_user} found in authorized IDs")
        else:
            print(f"❌ Target user {target_user} NOT found in authorized IDs")
            return False
        
        # Test authorization function
        print(f"🔐 Testing authorization for user {target_user}...")
        is_auth = is_authorized(target_user)
        
        if is_auth:
            print(f"✅ User {target_user} authorization: SUCCESS")
            return True
        else:
            print(f"❌ User {target_user} authorization: FAILED")
            return False
            
    except Exception as e:
        print(f"❌ Delivery bot authorization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_callback_authorization():
    """Test callback query authorization logic"""
    print("\n🔍 Test 3: Callback Authorization Logic")
    try:
        from src.bots.delivery_bot import is_authorized
        
        target_user = 5546595738
        
        # Simulate callback authorization check
        print(f"🔘 Simulating callback authorization for user {target_user}...")
        
        # This is the same check used in handle_order_decision
        auth_result = is_authorized(target_user)
        
        if auth_result:
            print(f"✅ Callback authorization would succeed for user {target_user}")
            print(f"   User would be able to accept/decline orders")
            return True
        else:
            print(f"❌ Callback authorization would fail for user {target_user}")
            print(f"   User would see 'Access Denied' message")
            return False
            
    except Exception as e:
        print(f"❌ Callback authorization test failed: {e}")
        return False

def test_cache_behavior():
    """Test authorization cache behavior"""
    print("\n🔍 Test 4: Authorization Cache Behavior")
    try:
        from src.bots.delivery_bot import (
            get_authorized_delivery_ids_from_firebase,
            clear_authorization_cache,
            is_authorized
        )
        
        target_user = 5546595738
        
        # Test 1: Fresh cache
        print("🔄 Testing fresh cache...")
        clear_authorization_cache()
        result1 = is_authorized(target_user)
        print(f"   Fresh cache result: {result1}")
        
        # Test 2: Cached result
        print("💾 Testing cached result...")
        result2 = is_authorized(target_user)
        print(f"   Cached result: {result2}")
        
        # Test 3: Cache refresh
        print("🔄 Testing cache refresh...")
        clear_authorization_cache()
        result3 = is_authorized(target_user)
        print(f"   Refreshed cache result: {result3}")
        
        # All results should be consistent
        if result1 == result2 == result3 == True:
            print("✅ Cache behavior is consistent")
            return True
        else:
            print(f"❌ Cache behavior is inconsistent: {result1}, {result2}, {result3}")
            return False
            
    except Exception as e:
        print(f"❌ Cache behavior test failed: {e}")
        return False

def test_management_bot_integration():
    """Test management bot integration"""
    print("\n🔍 Test 5: Management Bot Integration")
    try:
        from src.bots.management_bot import (
            get_authorized_delivery_ids,
            add_authorized_delivery_personnel,
            remove_authorized_delivery_personnel
        )
        
        print("✅ Management bot authorization functions imported")
        
        # Test getting authorized IDs from management bot perspective
        mgmt_authorized_ids = get_authorized_delivery_ids()
        print(f"📋 Management bot authorized IDs: {mgmt_authorized_ids}")
        
        target_user = 5546595738
        if target_user in mgmt_authorized_ids:
            print(f"✅ Target user {target_user} found in management bot authorized list")
            return True
        else:
            print(f"❌ Target user {target_user} NOT found in management bot authorized list")
            return False
            
    except Exception as e:
        print(f"❌ Management bot integration test failed: {e}")
        return False

def run_comprehensive_verification():
    """Run all verification tests"""
    print("🔧 COMPREHENSIVE AUTHORIZATION VERIFICATION")
    print("Testing authorization fix for user ID: 5546595738")
    print("=" * 70)
    
    tests = [
        ("Firebase Authorization Data", test_firebase_authorization),
        ("Delivery Bot Authorization", test_delivery_bot_authorization),
        ("Callback Authorization Logic", test_callback_authorization),
        ("Authorization Cache Behavior", test_cache_behavior),
        ("Management Bot Integration", test_management_bot_integration)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 70)
    print(f"📊 VERIFICATION RESULTS")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print(f"\n🎉 ALL VERIFICATION TESTS PASSED!")
        print(f"\n📋 AUTHORIZATION STATUS:")
        print(f"• ✅ User 5546595738 exists in Firebase with active status")
        print(f"• ✅ Delivery bot authorization cache working correctly")
        print(f"• ✅ Authorization checks consistent across all functions")
        print(f"• ✅ Callback query authorization will work properly")
        print(f"• ✅ Management bot integration is functional")
        print(f"\n🚀 EXPECTED PRODUCTION BEHAVIOR:")
        print(f"• User 5546595738 will receive order broadcasts")
        print(f"• Accept/Decline buttons will work without 'Access Denied' errors")
        print(f"• No more 'Unauthorized access attempt' warnings in logs")
        print(f"• Complete order workflow will function normally")
        print(f"\n📝 PRODUCTION TESTING:")
        print(f"1. Start delivery bot: python main.py --bot delivery")
        print(f"2. Have user 5546595738 interact with delivery bot")
        print(f"3. Test order acceptance/decline functionality")
        print(f"4. Monitor logs for successful authorization messages")
        
    else:
        print(f"\n⚠️ {failed} test(s) failed. Authorization may need additional fixes.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_comprehensive_verification()
    if success:
        print("\n✅ AUTHORIZATION VERIFICATION COMPLETE!")
        print("User 5546595738 should now have full delivery bot access.")
    else:
        print("\n❌ Authorization verification found issues.")
    sys.exit(0 if success else 1)
