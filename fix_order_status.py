#!/usr/bin/env python3
"""
Fix existing orders that have incorrect delivery_status
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def fix_order_statuses():
    """Fix orders that have missing or incorrect delivery_status"""
    print("🔧 FIXING ORDER STATUSES")
    print("=" * 30)
    
    try:
        from src.firebase_db import get_data, set_data
        
        confirmed_orders = get_data("confirmed_orders") or {}
        
        if not confirmed_orders:
            print("📭 No confirmed orders found")
            return True
        
        print(f"📋 Found {len(confirmed_orders)} confirmed orders")
        
        fixed_count = 0
        
        for order_number, order_data in confirmed_orders.items():
            delivery_status = order_data.get('delivery_status')
            status = order_data.get('status')
            assigned_to = order_data.get('assigned_to')
            
            needs_fix = False
            new_delivery_status = None
            
            # Determine what the delivery_status should be
            if not delivery_status or delivery_status == 'unknown':
                if assigned_to and assigned_to != 'none':
                    # Order is assigned
                    new_delivery_status = 'assigned'
                    needs_fix = True
                elif status in ['APPROVED', 'CONFIRMED']:
                    # Order is approved but not assigned
                    new_delivery_status = 'pending_assignment'
                    needs_fix = True
            
            if needs_fix:
                print(f"🔧 Fixing Order #{order_number}:")
                print(f"   Current status: {status}")
                print(f"   Current delivery_status: {delivery_status}")
                print(f"   Assigned to: {assigned_to}")
                print(f"   Setting delivery_status to: {new_delivery_status}")
                
                # Update the order
                order_data['delivery_status'] = new_delivery_status
                
                # Store back to Firebase
                if set_data(f"confirmed_orders/{order_number}", order_data):
                    print(f"   ✅ Successfully updated")
                    fixed_count += 1
                else:
                    print(f"   ❌ Failed to update")
                print()
        
        print(f"📊 SUMMARY: Fixed {fixed_count} orders")
        return True
        
    except Exception as e:
        print(f"❌ Error fixing order statuses: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_fixes():
    """Verify that the fixes were applied correctly"""
    print("✅ VERIFYING FIXES")
    print("=" * 20)
    
    try:
        from src.firebase_db import get_data
        
        confirmed_orders = get_data("confirmed_orders") or {}
        
        if not confirmed_orders:
            print("📭 No confirmed orders found")
            return True
        
        available_count = 0
        assigned_count = 0
        other_count = 0
        
        for order_number, order_data in confirmed_orders.items():
            delivery_status = order_data.get('delivery_status')
            
            if delivery_status == 'pending_assignment':
                available_count += 1
                print(f"✅ Order #{order_number}: Available for assignment")
            elif delivery_status == 'assigned':
                assigned_count += 1
                assigned_to = order_data.get('assigned_to', 'unknown')
                print(f"📋 Order #{order_number}: Assigned to {assigned_to}")
            elif delivery_status in ['completed', 'customer_confirmed']:
                other_count += 1
                print(f"🏁 Order #{order_number}: {delivery_status}")
            else:
                other_count += 1
                print(f"⚠️  Order #{order_number}: Unknown status - {delivery_status}")
        
        print(f"\n📊 VERIFICATION SUMMARY:")
        print(f"   • Available for assignment: {available_count}")
        print(f"   • Already assigned: {assigned_count}")
        print(f"   • Other statuses: {other_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying fixes: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assignment_logic():
    """Test the assignment logic with fixed orders"""
    print("\n🧪 TESTING ASSIGNMENT LOGIC")
    print("=" * 35)
    
    try:
        from src.firebase_db import get_data
        
        confirmed_orders = get_data("confirmed_orders") or {}
        
        available_orders = []
        for order_number, order_data in confirmed_orders.items():
            if order_data.get('delivery_status') == 'pending_assignment':
                available_orders.append(order_number)
        
        if available_orders:
            print(f"✅ Found {len(available_orders)} orders available for assignment:")
            for order_number in available_orders:
                print(f"   • Order #{order_number}")
            
            # Test the acceptance condition
            test_order = available_orders[0]
            order_data = confirmed_orders[test_order]
            
            print(f"\n🧪 Testing acceptance logic for Order #{test_order}:")
            
            # Simulate the delivery bot check
            if order_data.get('delivery_status') == 'pending_assignment':
                print("   ✅ Order would be accepted (delivery_status == 'pending_assignment')")
            else:
                print(f"   ❌ Order would be rejected (delivery_status == '{order_data.get('delivery_status')}')")
        else:
            print("📭 No orders available for assignment testing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing assignment logic: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main fix function"""
    print("🔧 ORDER STATUS FIX TOOL")
    print("=" * 40)
    print("Fixing orders with missing or incorrect delivery_status")
    print()
    
    # Run fixes
    fix_success = fix_order_statuses()
    
    if fix_success:
        print()
        verify_success = verify_fixes()
        
        if verify_success:
            test_success = test_assignment_logic()
            
            if test_success:
                print("\n" + "=" * 40)
                print("✅ ALL FIXES COMPLETED SUCCESSFULLY!")
                print("\n📋 WHAT WAS FIXED:")
                print("• Orders with missing delivery_status now have proper status")
                print("• Orders with 'unknown' delivery_status have been corrected")
                print("• Assignment logic should now work correctly")
                print("\n💡 TRY AGAIN:")
                print("• Orders should now be available for delivery personnel to accept")
                print("• The 'already assigned' error should be resolved")
            else:
                print("\n❌ Testing failed")
        else:
            print("\n❌ Verification failed")
    else:
        print("\n❌ Fix operation failed")
    
    return fix_success

if __name__ == "__main__":
    main()
