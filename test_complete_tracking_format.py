#!/usr/bin/env python3
"""
Test that all order tracking bot status updates use the complete format
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_complete_format_function():
    """Test the format_complete_order_details function"""
    print("📋 TESTING COMPLETE FORMAT FUNCTION")
    print("=" * 40)
    
    try:
        from src.bots.order_track_bot import format_complete_order_details
        
        # Create comprehensive test order data
        test_order_data = {
            'restaurant_id': '1',
            'phone_number': '+251912345678',
            'delivery_name': '<PERSON>',
            'delivery_location': None,
            'delivery_gate': 'Applied Library',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-01-15 14:30:00',
            'approved_at': '2024-01-15 14:35:00',
            'items': [
                {'name': 'Pizza Margherita', 'price': 120, 'quantity': 1},
                {'name': 'Coca Cola', 'price': 30, 'quantity': 1}
            ]
        }
        
        # Test the complete format
        message = format_complete_order_details(
            "TEST_001",
            test_order_data,
            "ORDER STATUS UPDATE",
            "Test status message",
            "• Test Timing: 2024-01-15 15:00:00\n"
        )
        
        print("📋 COMPLETE FORMAT MESSAGE:")
        print("-" * 30)
        print(message)
        
        # Verify all required elements are present
        required_elements = [
            "Order #TEST_001",
            "**Restaurant Area**",
            "**Customer Details:**",
            "**Phone**",
            "**Name**",
            "**Delivery Address**",
            "**Gate**",
            "**Order Items:**",
            "Pizza Margherita",
            "Coca Cola",
            "**Order Summary:**",
            "Subtotal: 150 Birr",
            "Delivery Fee: 25 Birr",
            "**Total Amount**: 175 Birr",
            "**Timing:**",
            "**Status**"
        ]
        
        missing_elements = []
        for element in required_elements:
            if element not in message:
                missing_elements.append(element)
        
        print(f"\n🔍 VERIFICATION:")
        print(f"   • Required elements: {len(required_elements)}")
        print(f"   • Missing elements: {len(missing_elements)}")
        
        if not missing_elements:
            print("   ✅ All required elements present")
        else:
            print(f"   ❌ Missing: {', '.join(missing_elements)}")
        
        # Check for proper delivery location (not "N/A")
        if "Applied Library" in message:
            print("   ✅ Actual delivery location shown (not N/A)")
        else:
            print("   ❌ Delivery location showing as N/A")
        
        return len(missing_elements) == 0
        
    except Exception as e:
        print(f"❌ Error testing complete format function: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_status_update_functions():
    """Test all status update notification functions"""
    print("\n🔔 TESTING STATUS UPDATE FUNCTIONS")
    print("=" * 40)
    
    try:
        # Import all notification functions
        from src.bots.order_track_bot import (
            send_order_status_update,
            notify_delivery_assignment,
            notify_delivery_accepted,
            notify_delivery_completed,
            notify_customer_confirmed,
            send_detailed_order_notification
        )
        
        import inspect
        
        # Test functions that should use complete format
        functions_to_test = [
            ("send_order_status_update", send_order_status_update),
            ("notify_delivery_assignment", notify_delivery_assignment),
            ("notify_delivery_accepted", notify_delivery_accepted),
            ("notify_delivery_completed", notify_delivery_completed),
            ("notify_customer_confirmed", notify_customer_confirmed),
            ("send_detailed_order_notification", send_detailed_order_notification)
        ]
        
        print("📋 Checking function implementations:")
        
        all_use_complete_format = True
        
        for func_name, func in functions_to_test:
            print(f"\n🔍 {func_name}:")
            
            # Get function source code
            source = inspect.getsource(func)
            
            # Check if it uses format_complete_order_details
            uses_complete_format = "format_complete_order_details" in source
            
            # Check if it has proper error handling
            has_error_handling = "try:" in source and "except" in source
            
            # Check if it gets complete order data
            gets_order_data = "get_data(f\"confirmed_orders/{order_number}\")" in source
            
            print(f"   • Uses complete format: {'✅' if uses_complete_format else '❌'}")
            print(f"   • Has error handling: {'✅' if has_error_handling else '❌'}")
            print(f"   • Gets order data: {'✅' if gets_order_data else '❌'}")
            
            if not uses_complete_format:
                all_use_complete_format = False
                print(f"   ⚠️  Function may not use complete format!")
        
        return all_use_complete_format
        
    except Exception as e:
        print(f"❌ Error testing status update functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_consistency():
    """Test that all status messages have consistent structure"""
    print("\n📊 TESTING MESSAGE CONSISTENCY")
    print("=" * 35)
    
    try:
        from src.bots.order_track_bot import format_complete_order_details
        
        # Create test order data
        test_order_data = {
            'restaurant_id': '1',
            'phone_number': '+251912345678',
            'delivery_name': 'John Doe',
            'delivery_location': None,
            'delivery_gate': 'Applied Library',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-01-15 14:30:00',
            'approved_at': '2024-01-15 14:35:00',
            'items': [
                {'name': 'Pizza Margherita', 'price': 120, 'quantity': 1}
            ]
        }
        
        # Test different status scenarios
        test_scenarios = [
            {
                'title': 'NEW ORDER AVAILABLE',
                'status': 'Payment Approved - Broadcasting to delivery personnel',
                'timing': ''
            },
            {
                'title': 'ORDER ASSIGNED TO DELIVERY PERSONNEL',
                'status': 'Assigned to John Smith (0912345678)',
                'timing': '• Assigned to Delivery: 2024-01-15 14:40:00\n'
            },
            {
                'title': 'ORDER ACCEPTED BY DELIVERY PERSONNEL',
                'status': 'Driver John Smith has accepted the order',
                'timing': '• Accepted by Driver: 2024-01-15 14:42:00\n'
            },
            {
                'title': 'DELIVERY COMPLETED',
                'status': 'Driver John Smith has marked the order as delivered',
                'timing': '• Completed by Driver: 2024-01-15 15:00:00\n'
            },
            {
                'title': 'ORDER FULLY COMPLETED',
                'status': 'Customer has confirmed receipt of the order',
                'timing': '• Customer Confirmed: 2024-01-15 15:05:00\n'
            },
            {
                'title': 'ORDER STATUS UPDATE',
                'status': 'Delivery Issue Reported - Investigation required',
                'timing': '• Status Updated: 2024-01-15 15:10:00\n'
            }
        ]
        
        print("📋 Testing message consistency across all status types:")
        
        consistent_structure = True
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🔍 Scenario {i}: {scenario['title']}")
            
            message = format_complete_order_details(
                "TEST_001",
                test_order_data,
                scenario['title'],
                scenario['status'],
                scenario['timing']
            )
            
            # Check for consistent structure elements
            structure_elements = [
                "Order #",
                "**Restaurant**:",
                "**Restaurant Area**:",
                "**Customer Details:**",
                "**Phone**:",
                "**Name**:",
                "**Delivery Address**:",
                "**Gate**:",
                "**Order Items:**",
                "**Order Summary:**",
                "Subtotal:",
                "Delivery Fee:",
                "**Total Amount**:",
                "**Timing:**",
                "**Status**:"
            ]
            
            missing_in_scenario = []
            for element in structure_elements:
                if element not in message:
                    missing_in_scenario.append(element)
            
            if not missing_in_scenario:
                print(f"   ✅ Complete structure maintained")
            else:
                print(f"   ❌ Missing: {', '.join(missing_in_scenario)}")
                consistent_structure = False
            
            # Check for actual delivery location (not N/A)
            if "Applied Library" in message:
                print(f"   ✅ Actual delivery location shown")
            else:
                print(f"   ❌ Delivery location showing as N/A")
                consistent_structure = False
        
        return consistent_structure
        
    except Exception as e:
        print(f"❌ Error testing message consistency: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔧 ORDER TRACKING BOT COMPLETE FORMAT VERIFICATION")
    print("=" * 60)
    print("Testing that all status updates use complete order details format")
    print()
    
    # Run all tests
    tests = [
        test_complete_format_function,
        test_status_update_functions,
        test_message_consistency
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    successful_tests = sum(results)
    total_tests = len(results)
    
    print(f"📊 TEST SUMMARY: {successful_tests}/{total_tests} tests passed")
    
    if all(results):
        print("✅ ALL TESTS PASSED - Complete format working correctly!")
        print("\n📋 VERIFIED FIXES:")
        print("• All status update functions use format_complete_order_details")
        print("• Restaurant area information properly displayed")
        print("• Actual delivery locations shown (not N/A)")
        print("• Complete customer details in all messages")
        print("• Full financial breakdown (subtotal + delivery fee + total)")
        print("• Complete timing information for order lifecycle")
        print("• Consistent message structure across all status types")
        print("\n🎯 EXPECTED RESULTS:")
        print("• Order tracking bot will show complete details for all statuses")
        print("• No more missing restaurant area information")
        print("• No more 'N/A' delivery locations")
        print("• Consistent administrative oversight across order lifecycle")
    else:
        print("❌ SOME TESTS FAILED - Review the output above")
        print("\n🔧 ISSUES TO ADDRESS:")
        if not results[0]:
            print("• Complete format function needs fixing")
        if not results[1]:
            print("• Some status update functions not using complete format")
        if not results[2]:
            print("• Message structure inconsistency detected")
    
    return all(results)

if __name__ == "__main__":
    main()
