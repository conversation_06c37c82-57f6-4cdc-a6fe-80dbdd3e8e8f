#!/usr/bin/env python3
"""
Test script to verify delivery bot callback handlers are properly registered.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_delivery_bot_callback_registration():
    """Test if delivery bot callback handlers are properly registered"""
    print("🚚 Testing Delivery Bot Callback Registration...")
    print("=" * 60)
    
    try:
        from src.bots.delivery_bot import delivery_bot
        print("✅ Delivery bot imported successfully")
        
        # Get callback handlers
        callback_handlers = delivery_bot._callback_query_handlers
        print(f"📋 Found {len(callback_handlers)} callback handlers")
        
        # Test callback data patterns
        test_patterns = [
            "accept_order_TEST123",
            "decline_order_TEST123", 
            "complete_order_TEST123",
            "accept_TEST123",
            "decline_TEST123"
        ]
        
        # Mock call object
        class MockCall:
            def __init__(self, data):
                self.data = data
        
        handler_found = False
        complete_order_supported = False
        
        for handler in callback_handlers:
            if hasattr(handler, 'func') and handler.func:
                # Test each pattern
                for pattern in test_patterns:
                    test_call = MockCall(pattern)
                    try:
                        if handler.func(test_call):
                            handler_found = True
                            if pattern.startswith('complete_order_'):
                                complete_order_supported = True
                            print(f"✅ Handler supports pattern: {pattern}")
                    except Exception as e:
                        # Expected for some patterns
                        pass
        
        if handler_found:
            print("✅ Order decision callback handler is registered")
        else:
            print("❌ Order decision callback handler NOT found")
            
        if complete_order_supported:
            print("✅ Complete order callback is supported")
        else:
            print("❌ Complete order callback NOT supported")
            
        return handler_found and complete_order_supported
        
    except Exception as e:
        print(f"❌ Error testing delivery bot callbacks: {e}")
        return False

def test_delivery_bot_message_editing():
    """Test delivery bot message editing capabilities"""
    print("\n📝 Testing Delivery Bot Message Editing...")
    print("=" * 60)
    
    try:
        from src.bots.delivery_bot import delivery_bot
        from telebot import types
        
        # Test creating inline keyboard
        markup = types.InlineKeyboardMarkup()
        btn = types.InlineKeyboardButton("🏁 Complete Order", callback_data="complete_order_TEST123")
        markup.add(btn)
        
        print("✅ Inline keyboard creation works")
        print(f"   Button text: {btn.text}")
        print(f"   Callback data: {btn.callback_data}")
        
        # Test message formatting
        test_message = "✅ **Order #TEST123 ACCEPTED**\n\nOriginal order details\n\n📋 **Status**: Order assigned to you\n🚚 Click 'Complete Order' when delivery is finished"
        
        if len(test_message) < 4096:  # Telegram message limit
            print("✅ Message formatting is within Telegram limits")
        else:
            print("❌ Message too long for Telegram")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing message editing: {e}")
        return False

def test_imports_and_dependencies():
    """Test all required imports for delivery bot functionality"""
    print("\n📦 Testing Imports and Dependencies...")
    print("=" * 60)
    
    try:
        # Test core imports
        from src.bots.delivery_bot import delivery_bot, logger
        print("✅ Core delivery bot imports work")
        
        # Test Firebase imports
        from src.firebase_db import get_data, set_data
        print("✅ Firebase imports work")
        
        # Test utility imports
        from src.utils.delivery_personnel_utils import (
            assign_order_to_personnel,
            get_real_time_capacity,
            update_assignment_status,
            get_delivery_personnel_by_telegram_id
        )
        print("✅ Delivery personnel utility imports work")
        
        # Test order tracking imports
        from src.bots.delivery_bot import get_order_tracking_notifications
        notify_accepted, notify_completed = get_order_tracking_notifications()
        if notify_accepted and notify_completed:
            print("✅ Order tracking notification imports work")
        else:
            print("⚠️  Order tracking notifications may have import issues")
        
        # Test telebot types
        from telebot import types
        print("✅ Telebot types import works")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing imports: {e}")
        return False

def main():
    """Run all delivery bot tests"""
    print("🧪 TESTING DELIVERY BOT COMPLETE ORDER FUNCTIONALITY")
    print("=" * 80)
    
    tests = [
        test_delivery_bot_callback_registration,
        test_delivery_bot_message_editing,
        test_imports_and_dependencies
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Delivery bot should be working correctly.")
        print("\n🔧 DEBUGGING SUGGESTIONS:")
        print("1. Check delivery bot logs for any runtime errors")
        print("2. Verify delivery personnel are properly authenticated")
        print("3. Test with a real order to see actual behavior")
        print("4. Check if message editing fails due to Telegram API limits")
    else:
        print("⚠️  Some tests failed. Check the specific errors above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
