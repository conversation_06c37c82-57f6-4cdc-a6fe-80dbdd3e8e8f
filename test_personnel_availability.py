#!/usr/bin/env python3
"""
Test if the new delivery personnel is now available for broadcasts
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_personnel_availability():
    """Test if personnel 1133538088 is now available"""
    print("=== TESTING PERSONNEL AVAILABILITY ===")
    
    try:
        # Import required modules
        from src.firebase_db import get_data
        from src.data_models import delivery_personnel, delivery_personnel_availability, delivery_personnel_capacity
        from src.data_storage import load_user_data
        
        # 1. Load fresh data from Firebase
        print("1. Loading fresh data from Firebase...")
        load_user_data()
        
        # 2. Check in-memory data structures
        print(f"\n2. Checking in-memory data structures...")
        print(f"Personnel count: {len(delivery_personnel)}")
        print(f"Availability count: {len(delivery_personnel_availability)}")
        print(f"Capacity count: {len(delivery_personnel_capacity)}")
        
        # 3. Find our target personnel
        target_telegram_id = "1133538088"
        target_personnel_id = None
        
        for pid, pdata in delivery_personnel.items():
            if pdata.get('telegram_id') == target_telegram_id:
                target_personnel_id = pid
                print(f"\n✅ Found target personnel: {pid}")
                print(f"  Name: {pdata.get('name')}")
                print(f"  Status: {pdata.get('status')}")
                print(f"  Verified: {pdata.get('is_verified')}")
                print(f"  Service Areas: {pdata.get('service_areas')}")
                break
        
        if not target_personnel_id:
            print(f"❌ Personnel with Telegram ID {target_telegram_id} not found in memory")
            return False
        
        # Check availability and capacity in memory
        availability = delivery_personnel_availability.get(target_personnel_id, "NOT_SET")
        capacity = delivery_personnel_capacity.get(target_personnel_id, "NOT_SET")
        
        print(f"  Availability: {availability}")
        print(f"  Capacity: {capacity}")
        
        # 4. Test find_available_personnel function
        print(f"\n3. Testing find_available_personnel function...")
        
        try:
            from src.utils.delivery_personnel_utils import find_available_personnel
            
            for area_id in ['1', '2', '3', '4']:
                available_personnel = find_available_personnel(area_id)
                print(f"Area {area_id}: {len(available_personnel)} personnel - {available_personnel}")
                
                if target_personnel_id in available_personnel:
                    print(f"  ✅ {target_personnel_id} ({target_telegram_id}) is available for area {area_id}")
                    return True
                else:
                    print(f"  ❌ {target_personnel_id} ({target_telegram_id}) is NOT available for area {area_id}")
            
            print(f"\n❌ Personnel {target_personnel_id} ({target_telegram_id}) is NOT available for any area")
            
            # 5. Debug why personnel is not available
            print(f"\n4. Debugging availability criteria...")
            
            if target_personnel_id in delivery_personnel:
                from src.data_models import DeliveryPersonnel
                personnel_data = delivery_personnel[target_personnel_id]
                personnel = DeliveryPersonnel.from_dict(personnel_data)
                
                print(f"Personnel object:")
                print(f"  Status: {personnel.status}")
                print(f"  Verified: {personnel.is_verified}")
                print(f"  Current Capacity: {personnel.current_capacity}")
                print(f"  Max Capacity: {personnel.max_capacity}")
                print(f"  Service Areas: {personnel.service_areas}")
                
                # Check individual availability criteria
                status_ok = personnel.status == "available"
                verified_ok = personnel.is_verified
                capacity_ok = personnel.current_capacity < personnel.max_capacity
                availability_ok = delivery_personnel_availability.get(target_personnel_id) == "available"
                
                print(f"\nAvailability criteria:")
                print(f"  status == 'available': {status_ok}")
                print(f"  is_verified: {verified_ok}")
                print(f"  current_capacity < max_capacity: {capacity_ok}")
                print(f"  availability == 'available': {availability_ok}")
                
                is_available = personnel.is_available()
                print(f"  personnel.is_available(): {is_available}")
                
                # Test can_serve_area for each area
                print(f"\nArea service capability:")
                for area in ['1', '2', '3', '4']:
                    can_serve = personnel.can_serve_area(area)
                    print(f"  Area {area}: {can_serve}")
                
                # Overall availability check
                overall_available = (
                    is_available and
                    availability_ok and
                    '1' in personnel.service_areas  # Check if can serve area 1
                )
                
                print(f"\nOverall availability for area 1: {overall_available}")
                
                if not overall_available:
                    print(f"\n🔍 ISSUE IDENTIFIED:")
                    if not status_ok:
                        print(f"  - Status is '{personnel.status}', should be 'available'")
                    if not verified_ok:
                        print(f"  - Personnel is not verified")
                    if not capacity_ok:
                        print(f"  - Capacity issue: {personnel.current_capacity}/{personnel.max_capacity}")
                    if not availability_ok:
                        print(f"  - Availability data is '{delivery_personnel_availability.get(target_personnel_id)}', should be 'available'")
                    if '1' not in personnel.service_areas:
                        print(f"  - Personnel cannot serve area 1. Service areas: {personnel.service_areas}")
                
                return overall_available
            else:
                print(f"❌ Personnel {target_personnel_id} not found in delivery_personnel dict")
                return False
                
        except Exception as e:
            print(f"❌ Error testing find_available_personnel: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🧪 TESTING PERSONNEL AVAILABILITY AFTER DATA RELOAD")
    print("=" * 60)
    
    success = test_personnel_availability()
    
    if success:
        print(f"\n🎉 SUCCESS: Personnel 1133538088 is now available for delivery broadcasts!")
    else:
        print(f"\n❌ FAILED: Personnel 1133538088 is still not available for delivery broadcasts")
        print(f"Additional investigation needed to identify the root cause.")
