#!/usr/bin/env python3
"""
Test the fixed delivery location formatting
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_fixed_formatting_functions():
    """Test the fixed location formatting functions"""
    print("🧪 TESTING FIXED LOCATION FORMATTING")
    print("=" * 40)
    
    try:
        # Import the fixed functions
        from src.bots.delivery_bot import format_delivery_location_for_bot
        from src.handlers.payment_handlers import format_delivery_location
        
        print("✅ Successfully imported fixed formatting functions")
        
        # Test with actual order data patterns
        test_cases = [
            {
                "name": "Typical order (location in gate field)",
                "delivery_location": None,
                "delivery_gate": "Applied Library",
                "expected": "Applied Library"
            },
            {
                "name": "Another typical order",
                "delivery_location": None,
                "delivery_gate": "Masters Dorm (Lebs Matebiya)",
                "expected": "Masters Dorm (Lebs Matebiya)"
            },
            {
                "name": "Federal Dorm order",
                "delivery_location": None,
                "delivery_gate": "Federal Dorm",
                "expected": "Federal Dorm"
            },
            {
                "name": "Location in both fields",
                "delivery_location": "Building B",
                "delivery_gate": "Main Gate",
                "expected": "Main Gate"  # Gate takes priority
            },
            {
                "name": "Location only in delivery_location",
                "delivery_location": "Building A",
                "delivery_gate": None,
                "expected": "Building A"
            },
            {
                "name": "Both fields empty",
                "delivery_location": None,
                "delivery_gate": None,
                "expected": "Location not specified"
            },
            {
                "name": "N/A values",
                "delivery_location": "N/A",
                "delivery_gate": "N/A",
                "expected": "Location not specified"
            }
        ]
        
        print("\n🧪 Testing delivery bot formatting function:")
        all_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            name = test_case["name"]
            delivery_location = test_case["delivery_location"]
            delivery_gate = test_case["delivery_gate"]
            expected = test_case["expected"]
            
            result = format_delivery_location_for_bot(delivery_location, delivery_gate)
            
            status = "✅" if result == expected else "❌"
            if result != expected:
                all_passed = False
            
            print(f"   {status} Test {i} ({name}):")
            print(f"      Input: location='{delivery_location}', gate='{delivery_gate}'")
            print(f"      Result: '{result}' (expected: '{expected}')")
            print()
        
        print("🧪 Testing payment handlers formatting function:")
        
        for i, test_case in enumerate(test_cases, 1):
            name = test_case["name"]
            delivery_location = test_case["delivery_location"]
            delivery_gate = test_case["delivery_gate"]
            expected = test_case["expected"]
            
            result = format_delivery_location(delivery_location, delivery_gate)
            
            status = "✅" if result == expected else "❌"
            if result != expected:
                all_passed = False
            
            print(f"   {status} Test {i} ({name}):")
            print(f"      Input: location='{delivery_location}', gate='{delivery_gate}'")
            print(f"      Result: '{result}' (expected: '{expected}')")
            print()
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error testing formatting functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_real_order_data():
    """Test with actual order data from Firebase"""
    print("🔍 TESTING WITH REAL ORDER DATA")
    print("=" * 35)
    
    try:
        from src.firebase_db import get_data
        from src.bots.delivery_bot import format_delivery_location_for_bot
        from src.handlers.payment_handlers import format_delivery_location
        
        confirmed_orders = get_data("confirmed_orders") or {}
        
        if not confirmed_orders:
            print("📭 No confirmed orders found")
            return True
        
        print(f"📋 Testing with {len(confirmed_orders)} real orders:")
        print()
        
        for order_number, order_data in confirmed_orders.items():
            delivery_location = order_data.get('delivery_location')
            delivery_gate = order_data.get('delivery_gate')
            
            # Test delivery bot formatting
            bot_result = format_delivery_location_for_bot(delivery_location, delivery_gate)
            
            # Test payment handlers formatting
            handler_result = format_delivery_location(delivery_location, delivery_gate)
            
            print(f"📋 Order #{order_number}:")
            print(f"   • Raw data: location='{delivery_location}', gate='{delivery_gate}'")
            print(f"   • Delivery bot format: '{bot_result}'")
            print(f"   • Tracking bot format: '{handler_result}'")
            
            if bot_result == "Location not specified":
                print(f"   ❌ Still showing 'Location not specified'")
            else:
                print(f"   ✅ Now showing actual location")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing with real data: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_message_formatting():
    """Test the complete message formatting with fixed location"""
    print("📱 TESTING COMPLETE MESSAGE FORMATTING")
    print("=" * 40)
    
    try:
        from src.handlers.payment_handlers import format_delivery_bot_message, format_order_tracking_bot_message
        
        # Create test order data based on real patterns
        test_order_data = {
            'restaurant_id': '1',
            'phone_number': '+251912345678',
            'delivery_name': 'John Doe',
            'delivery_location': None,  # This is None in real data
            'delivery_gate': 'Applied Library',  # This contains the actual address
            'area_name': 'Campus',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-01-15 14:30:00'
        }
        
        items_text = "📋 **Order Items:**\n• Pizza Margherita x1 - 120 Birr\n• Coca Cola x1 - 30 Birr\n\n"
        
        print("📱 DELIVERY BOT MESSAGE (Privacy Protected):")
        print("-" * 45)
        delivery_message = format_delivery_bot_message(
            "TEST_001",
            "Pizza Palace",
            test_order_data,
            items_text,
            "2024-01-15 14:35:00"
        )
        print(delivery_message)
        
        print("\n🖥️ ORDER TRACKING BOT MESSAGE (Full Details):")
        print("-" * 45)
        tracking_message = format_order_tracking_bot_message(
            "TEST_001",
            "Pizza Palace",
            test_order_data,
            items_text,
            "2024-01-15 14:35:00"
        )
        print(tracking_message)
        
        # Check if the actual location appears in the messages
        expected_location = "Applied Library"
        
        delivery_has_location = expected_location in delivery_message
        tracking_has_location = expected_location in tracking_message
        
        print(f"\n📍 LOCATION VERIFICATION:")
        print(f"   • Delivery bot shows '{expected_location}': {'✅' if delivery_has_location else '❌'}")
        print(f"   • Tracking bot shows '{expected_location}': {'✅' if tracking_has_location else '❌'}")
        
        return delivery_has_location and tracking_has_location
        
    except Exception as e:
        print(f"❌ Error testing message formatting: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔧 DELIVERY LOCATION FIX VERIFICATION")
    print("=" * 50)
    print("Testing the fixed delivery location formatting functions")
    print()
    
    # Run all tests
    tests = [
        test_fixed_formatting_functions,
        test_with_real_order_data,
        test_message_formatting
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
            print()
        except Exception as e:
            print(f"❌ Test failed: {e}")
            results.append(False)
            print()
    
    print("=" * 50)
    successful_tests = sum(results)
    total_tests = len(results)
    
    print(f"📊 TEST SUMMARY: {successful_tests}/{total_tests} tests passed")
    
    if all(results):
        print("✅ ALL TESTS PASSED - Location formatting is now working!")
        print("\n📋 FIXES APPLIED:")
        print("• Fixed format_delivery_location_for_bot() in delivery bot")
        print("• Fixed format_delivery_location() in payment handlers")
        print("• Fixed format_delivery_location() in order tracking bot")
        print("• Now uses delivery_gate field as primary location source")
        print("• Falls back to delivery_location if delivery_gate is empty")
        print("\n🎯 EXPECTED RESULTS:")
        print("• Delivery bot will show actual addresses like 'Applied Library'")
        print("• Order tracking bot will show actual addresses")
        print("• No more 'Location not specified' for valid orders")
        print("• Privacy controls remain intact")
    else:
        print("❌ SOME TESTS FAILED - Review the output above")
    
    return all(results)

if __name__ == "__main__":
    main()
