#!/usr/bin/env python3
"""
Verification script for delivery fee calculation fixes.
This script verifies the code changes without requiring Firebase connection.
"""

import sys
import os
import re

def check_handle_delivery_gate_fix():
    """Check if handle_delivery_gate function has been fixed"""
    print("🔍 Checking handle_delivery_gate function fix...")
    
    try:
        with open('src/handlers/order_handlers.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the key improvements
        improvements = [
            (r'fee = get_delivery_fee\(restaurant_area_id, location_id\)', 
             'Uses get_delivery_fee() instead of parsing from text'),
            (r'logger\.info\(f"Retrieved delivery fee from Firebase:', 
             'Logs delivery fee retrieval'),
            (r'if fee <= 0:', 
             'Validates delivery fee'),
            (r'from src\.config import delivery_fees', 
             'Has fallback to legacy config'),
            (r'logger\.info\(f"Using legacy delivery fee:', 
             'Logs fallback usage')
        ]
        
        all_found = True
        for pattern, description in improvements:
            if re.search(pattern, content):
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ Missing: {description}")
                all_found = False
        
        # Check that old parsing logic is removed/replaced
        old_patterns = [
            r'fee_text = gate_text\.split\("\("\)\[1\]\.split\(" birr\)"\)\[0\]',
            r'fee = float\(fee_text\)'
        ]
        
        old_logic_found = False
        for pattern in old_patterns:
            if re.search(pattern, content):
                print(f"  ⚠️ Old parsing logic still present: {pattern}")
                old_logic_found = True
        
        if not old_logic_found:
            print("  ✅ Old text parsing logic removed")
        
        return all_found and not old_logic_found
        
    except Exception as e:
        print(f"  ❌ Error checking file: {e}")
        return False

def check_order_summary_validation():
    """Check if order summary has delivery fee validation"""
    print("\n🔍 Checking order summary validation...")
    
    try:
        with open('src/handlers/order_handlers.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for validation in generate_and_send_order_summary
        validations = [
            (r'logger\.info\(f"Order summary for user.*delivery_fee=', 
             'Logs delivery fee in order summary'),
            (r'if delivery_fee <= 0:', 
             'Validates delivery fee in summary'),
            (r'recalculated_fee = get_delivery_fee\(restaurant_area_id, delivery_location_id\)', 
             'Recalculates fee if invalid'),
            (r'order\["delivery_fee"\] = delivery_fee.*# Update the order data', 
             'Updates order data with recalculated fee')
        ]
        
        all_found = True
        for pattern, description in validations:
            if re.search(pattern, content):
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ Missing: {description}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ❌ Error checking file: {e}")
        return False

def check_admin_review_validation():
    """Check if admin review has delivery fee validation"""
    print("\n🔍 Checking admin review validation...")
    
    try:
        with open('src/handlers/admin_handlers.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for validation in send_order_for_review
        validations = [
            (r'logger\.info\(f"Admin review for order.*delivery_fee=', 
             'Logs delivery fee in admin review'),
            (r'if delivery_fee <= 0:', 
             'Validates delivery fee in admin review'),
            (r'recalculated_fee = get_delivery_fee\(restaurant_area_id, delivery_location_id\)', 
             'Recalculates fee if invalid in admin review')
        ]
        
        all_found = True
        for pattern, description in validations:
            if re.search(pattern, content):
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ Missing: {description}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ❌ Error checking file: {e}")
        return False

def check_firebase_storage_validation():
    """Check if Firebase storage has delivery fee validation"""
    print("\n🔍 Checking Firebase storage validation...")
    
    try:
        with open('src/handlers/payment_handlers.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for validation before Firebase storage
        validations = [
            (r'logger\.info\(f"Storing order.*in Firebase with delivery_fee=', 
             'Logs delivery fee before Firebase storage'),
            (r'if delivery_fee <= 0:.*when storing order.*in Firebase', 
             'Validates delivery fee before Firebase storage'),
            (r'recalculated_fee = get_delivery_fee\(restaurant_area_id, delivery_location_id\)', 
             'Recalculates fee if invalid before Firebase storage'),
            (r'logger\.info\(f"Stored order.*with delivery_fee=', 
             'Logs final delivery fee after Firebase storage')
        ]
        
        all_found = True
        for pattern, description in validations:
            if re.search(pattern, content, re.DOTALL):
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ Missing: {description}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ❌ Error checking file: {e}")
        return False

def check_delivery_bot_integration():
    """Check if delivery bot properly handles delivery fees"""
    print("\n🔍 Checking delivery bot integration...")
    
    try:
        with open('src/bots/delivery_bot.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for delivery fee handling in delivery bot
        checks = [
            (r'delivery_fee = order_data\.get\(\'delivery_fee\', 0\)', 
             'Retrieves delivery fee from order data'),
            (r'if delivery_fee > 0 and isinstance\(delivery_fee, \(int, float\)\):', 
             'Validates delivery fee type and value'),
            (r'logger\.warning\(f"Invalid delivery fee for order.*delivery_fee', 
             'Logs invalid delivery fees')
        ]
        
        all_found = True
        for pattern, description in checks:
            if re.search(pattern, content):
                print(f"  ✅ {description}")
            else:
                print(f"  ❌ Missing: {description}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"  ❌ Error checking file: {e}")
        return False

def main():
    """Run all verification checks"""
    print("🚀 Verifying delivery fee calculation fixes...")
    print("=" * 60)
    
    checks = [
        ("Handle Delivery Gate Fix", check_handle_delivery_gate_fix),
        ("Order Summary Validation", check_order_summary_validation),
        ("Admin Review Validation", check_admin_review_validation),
        ("Firebase Storage Validation", check_firebase_storage_validation),
        ("Delivery Bot Integration", check_delivery_bot_integration)
    ]
    
    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"❌ Error in {check_name}: {e}")
            results.append((check_name, False))
    
    # Summary
    print("\n" + "=" * 60)
    print("VERIFICATION SUMMARY")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for check_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{check_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} checks passed")
    
    if passed == total:
        print("\n🎉 All delivery fee fixes verified successfully!")
        print("\n📋 Summary of fixes:")
        print("  • Fixed handle_delivery_gate to use get_delivery_fee() instead of text parsing")
        print("  • Added delivery fee validation in order summary generation")
        print("  • Added delivery fee validation in admin review process")
        print("  • Added delivery fee validation before Firebase storage")
        print("  • Verified delivery bot properly handles delivery fees")
        print("\n✅ The delivery fee calculation bug should now be fixed!")
        return True
    else:
        print(f"\n💥 {total - passed} verification check(s) failed!")
        print("❌ Some fixes may not be properly implemented.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
