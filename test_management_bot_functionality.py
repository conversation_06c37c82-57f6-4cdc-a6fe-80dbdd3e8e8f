#!/usr/bin/env python3
"""
Test management bot functionality to ensure all fixes are working correctly.
Tests real-time data updates, personnel management, and analytics.
"""

import sys
import os
import time
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_personnel_management_real_time():
    """Test personnel management with real-time data updates"""
    print("👥 Testing Personnel Management Real-time Updates...")
    
    try:
        from src.bots.management_bot import refresh_personnel_data, refresh_availability_data
        from src.utils.delivery_personnel_utils import (
            refresh_delivery_personnel_data,
            find_available_personnel_with_capacity_check,
            get_real_time_capacity
        )
        
        # Test data refresh
        print("  🔄 Testing data refresh...")
        personnel_data = refresh_personnel_data()
        availability_data = refresh_availability_data()
        
        print(f"    - Personnel records: {len(personnel_data)}")
        print(f"    - Availability records: {len(availability_data)}")
        
        # Test delivery personnel data refresh
        print("  🔄 Testing delivery personnel data refresh...")
        refresh_result = refresh_delivery_personnel_data()
        print(f"    - Refresh successful: {refresh_result}")
        
        # Test real-time capacity checking
        print("  📊 Testing real-time capacity checking...")
        if personnel_data:
            sample_personnel_id = list(personnel_data.keys())[0]
            capacity = get_real_time_capacity(sample_personnel_id)
            print(f"    - Sample personnel {sample_personnel_id} capacity: {capacity}")
        
        # Test order broadcasting logic
        print("  📡 Testing order broadcasting logic...")
        available_personnel = find_available_personnel_with_capacity_check("1")
        print(f"    - Available personnel for area 1: {len(available_personnel)}")
        
        print("  ✅ Personnel management real-time tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_analytics_real_time():
    """Test analytics with real-time data refresh"""
    print("\n📊 Testing Analytics Real-time Updates...")
    
    try:
        from src.bots.management_bot import (
            refresh_analytics_data,
            validate_analytics_data,
            safe_get_numeric_value,
            safe_calculate_percentage
        )
        
        # Test analytics data refresh
        print("  🔄 Testing analytics data refresh...")
        analytics_data = refresh_analytics_data()
        
        for collection, data in analytics_data.items():
            print(f"    - {collection}: {len(data)} records")
        
        # Test data validation
        print("  ✅ Testing analytics data validation...")
        completed_orders = analytics_data.get('completed_orders', {})
        validated_orders = validate_analytics_data(completed_orders, "completed_orders")
        print(f"    - Validated {len(validated_orders)} completed orders")
        
        # Test safe numeric operations
        print("  🔢 Testing safe numeric operations...")
        if validated_orders:
            sample_order = list(validated_orders.values())[0]
            subtotal = safe_get_numeric_value(sample_order, 'subtotal', 0)
            delivery_fee = safe_get_numeric_value(sample_order, 'delivery_fee', 0)
            print(f"    - Sample order subtotal: {subtotal}, delivery fee: {delivery_fee}")
        
        # Test percentage calculations
        percentage = safe_calculate_percentage(25, 100, 0)
        print(f"    - Percentage calculation test: {percentage}%")
        
        print("  ✅ Analytics real-time tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_firebase_operations():
    """Test Firebase operations with validation and error handling"""
    print("\n🔥 Testing Firebase Operations...")
    
    try:
        from src.bots.management_bot import (
            validate_firebase_operation,
            safe_firebase_set,
            safe_firebase_update,
            invalidate_personnel_cache
        )
        from src.firebase_db import get_data, set_data
        
        # Test validation
        print("  ✅ Testing operation validation...")
        
        # Valid operations
        valid_tests = [
            ("set", "test/valid/path", {"data": "test"}),
            ("get", "test/path", None),
            ("update", "test/path", {"update": "data"})
        ]
        
        for op, path, data in valid_tests:
            result = validate_firebase_operation(op, path, data)
            print(f"    - {op} operation validation: {result}")
        
        # Invalid operations
        invalid_tests = [
            ("set", "../dangerous/path", {"data": "test"}),
            ("set", "test//double//slash", {"data": "test"}),
            ("set", "valid/path", None),  # No data for set
        ]
        
        for op, path, data in invalid_tests:
            result = validate_firebase_operation(op, path, data)
            print(f"    - Invalid {op} rejected: {not result}")
        
        # Test cache invalidation
        print("  🗑️ Testing cache invalidation...")
        invalidate_personnel_cache()
        print("    - Cache invalidation completed")
        
        print("  ✅ Firebase operations tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_data_consistency():
    """Test data consistency across different sources"""
    print("\n🔄 Testing Data Consistency...")
    
    try:
        from src.bots.management_bot import refresh_personnel_data, refresh_analytics_data
        from src.utils.delivery_personnel_utils import refresh_delivery_personnel_data
        from src.firebase_db import get_data
        
        # Get data from different sources
        print("  📊 Comparing data from different sources...")
        
        # Management bot data
        mgmt_personnel = refresh_personnel_data()
        mgmt_analytics = refresh_analytics_data()
        
        # Delivery utils data
        refresh_delivery_personnel_data()
        from src.data_models import delivery_personnel
        
        # Direct Firebase data
        firebase_personnel = get_data("delivery_personnel") or {}
        
        # Compare counts
        print(f"    - Management bot personnel: {len(mgmt_personnel)}")
        print(f"    - Delivery utils personnel: {len(delivery_personnel)}")
        print(f"    - Direct Firebase personnel: {len(firebase_personnel)}")
        
        # Check consistency
        consistent = (len(mgmt_personnel) == len(delivery_personnel) == len(firebase_personnel))
        print(f"    - Data consistency: {'✅ CONSISTENT' if consistent else '❌ INCONSISTENT'}")
        
        if not consistent:
            print("    - ⚠️  Data sources have different counts - this may indicate caching issues")
        
        print("  ✅ Data consistency tests completed!")
        return consistent
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def test_management_bot_integration():
    """Test management bot integration and menu functionality"""
    print("\n🤖 Testing Management Bot Integration...")
    
    try:
        from src.bots.management_bot import (
            get_management_bot,
            create_main_menu_keyboard,
            escape_markdown
        )
        
        # Test bot instance
        print("  🤖 Testing bot instance...")
        management_bot = get_management_bot()
        print(f"    - Management bot available: {management_bot is not None}")
        
        if management_bot:
            print(f"    - Bot username: {getattr(management_bot, 'username', 'Unknown')}")
        
        # Test menu creation
        print("  📋 Testing menu creation...")
        keyboard = create_main_menu_keyboard()
        print(f"    - Main menu keyboard created: {keyboard is not None}")
        
        # Test markdown escaping
        print("  📝 Testing markdown escaping...")
        test_strings = [
            "Normal text",
            "Text with $pecial characters",
            "Text with (parentheses) and [brackets]",
            "Text with * asterisks * and _ underscores _"
        ]
        
        for test_str in test_strings:
            escaped = escape_markdown(test_str)
            print(f"    - '{test_str}' -> '{escaped}'")
        
        print("  ✅ Management bot integration tests passed!")
        return True
        
    except Exception as e:
        print(f"  ❌ Error: {e}")
        return False

def main():
    """Run all functionality tests"""
    print("🚀 Management Bot Functionality Tests")
    print("=" * 60)
    
    tests = [
        ("Personnel Management Real-time", test_personnel_management_real_time),
        ("Analytics Real-time", test_analytics_real_time),
        ("Firebase Operations", test_firebase_operations),
        ("Data Consistency", test_data_consistency),
        ("Management Bot Integration", test_management_bot_integration)
    ]
    
    passed = 0
    total = len(tests)
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed += 1
                results.append(f"✅ {test_name}")
            else:
                results.append(f"❌ {test_name}")
        except Exception as e:
            print(f"  ❌ Test '{test_name}' failed with exception: {e}")
            results.append(f"❌ {test_name} (Exception)")
    
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    for result in results:
        print(f"  {result}")
    
    print(f"\n🏆 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All functionality tests passed!")
        print("✅ Management bot is working correctly with all fixes applied")
        return True
    else:
        print("⚠️  Some tests failed - review issues above")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Functionality Tests {'PASSED' if success else 'FAILED'}")
