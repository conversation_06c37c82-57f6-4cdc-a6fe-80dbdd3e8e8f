#!/usr/bin/env python3
"""
Test the complete personnel addition workflow to verify authorization synchronization
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_personnel_workflow():
    """Test the complete workflow: Personnel Addition → Authorization → Delivery Bot Access"""
    print("🔄 TESTING COMPLETE PERSONNEL WORKFLOW")
    print("=" * 60)
    
    try:
        # Import required functions
        from src.bots.management_bot import (
            add_authorized_delivery_personnel,
            get_authorized_delivery_ids,
            remove_authorized_delivery_personnel,
            validate_telegram_id
        )
        from src.bots.delivery_bot import (
            is_authorized,
            clear_authorization_cache
        )
        
        print("✅ All required functions imported successfully")
        
        # Test data
        test_telegram_id = "555666777"
        test_name = "Test Delivery Person"
        admin_id = 7729984017
        
        print(f"\n📋 Test Data:")
        print(f"• Telegram ID: {test_telegram_id}")
        print(f"• Name: {test_name}")
        print(f"• Admin ID: {admin_id}")
        
        # Step 1: Validate Telegram ID
        print(f"\n🔍 Step 1: Validating Telegram ID...")
        valid, tid = validate_telegram_id(test_telegram_id)
        if not valid:
            print(f"❌ Invalid Telegram ID: {test_telegram_id}")
            return False
        print(f"✅ Telegram ID validated: {tid}")
        
        # Step 2: Check initial authorization status
        print(f"\n🔍 Step 2: Checking initial authorization...")
        initial_auth = is_authorized(int(test_telegram_id))
        print(f"✅ Initial authorization status: {initial_auth}")
        
        # Step 3: Get initial authorized IDs count
        print(f"\n🔍 Step 3: Getting initial authorized IDs...")
        initial_ids = get_authorized_delivery_ids()
        initial_count = len(initial_ids)
        print(f"✅ Initial authorized IDs count: {initial_count}")
        
        # Step 4: Add personnel to authorization (simulate personnel addition)
        print(f"\n➕ Step 4: Adding personnel to authorization...")
        auth_success = add_authorized_delivery_personnel(test_telegram_id, test_name, admin_id)
        if not auth_success:
            print("❌ Failed to add personnel to authorization")
            return False
        print(f"✅ Personnel added to authorization successfully")
        
        # Step 5: Clear cache to force refresh
        print(f"\n🔄 Step 5: Clearing authorization cache...")
        clear_authorization_cache()
        print(f"✅ Authorization cache cleared")
        
        # Step 6: Check authorization after addition
        print(f"\n🔍 Step 6: Checking authorization after addition...")
        final_auth = is_authorized(int(test_telegram_id))
        print(f"✅ Final authorization status: {final_auth}")
        
        # Step 7: Verify authorized IDs count increased
        print(f"\n🔍 Step 7: Verifying authorized IDs count...")
        final_ids = get_authorized_delivery_ids()
        final_count = len(final_ids)
        print(f"✅ Final authorized IDs count: {final_count}")
        
        # Step 8: Clean up - remove test personnel
        print(f"\n🧹 Step 8: Cleaning up test data...")
        cleanup_success = remove_authorized_delivery_personnel(test_telegram_id, admin_id)
        if cleanup_success:
            print(f"✅ Test personnel removed successfully")
        else:
            print(f"⚠️ Test personnel cleanup failed (acceptable)")
        
        # Final verification
        clear_authorization_cache()
        cleanup_auth = is_authorized(int(test_telegram_id))
        print(f"✅ Authorization after cleanup: {cleanup_auth}")
        
        # Workflow verification
        workflow_success = (
            valid and  # ID validation worked
            auth_success and  # Authorization addition worked
            final_auth and  # Personnel can access delivery bot
            final_count > initial_count  # Authorized count increased
        )
        
        print(f"\n📊 WORKFLOW VERIFICATION:")
        print(f"• ID Validation: {'✅' if valid else '❌'}")
        print(f"• Authorization Addition: {'✅' if auth_success else '❌'}")
        print(f"• Delivery Bot Access: {'✅' if final_auth else '❌'}")
        print(f"• Count Increase: {'✅' if final_count > initial_count else '❌'}")
        
        if workflow_success:
            print(f"\n🎉 COMPLETE WORKFLOW SUCCESS!")
            print(f"✅ Personnel addition → authorization → delivery bot access WORKING")
            print(f"✅ The 'save_data is not defined' error has been FIXED")
            print(f"✅ Authorization synchronization is OPERATIONAL")
            
            print(f"\n🚀 PRODUCTION READY WORKFLOW:")
            print(f"1. Add Personnel (Management Bot) → ✅ Success")
            print(f"2. Authorization Added (Firebase) → ✅ Success")
            print(f"3. Cache Refresh (Delivery Bot) → ✅ Success")
            print(f"4. Immediate Access (Delivery Bot) → ✅ Success")
            
            return True
        else:
            print(f"\n❌ WORKFLOW FAILED")
            print(f"Some steps in the authorization workflow are not working correctly")
            return False
            
    except Exception as e:
        print(f"❌ Workflow test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_error_scenarios():
    """Test error handling scenarios"""
    print(f"\n🧪 TESTING ERROR SCENARIOS")
    print("=" * 40)
    
    try:
        from src.bots.management_bot import (
            add_authorized_delivery_personnel,
            validate_telegram_id
        )
        
        # Test invalid Telegram ID
        print("🔍 Testing invalid Telegram ID...")
        valid, _ = validate_telegram_id("invalid")
        print(f"✅ Invalid ID rejected: {not valid}")
        
        # Test empty name
        print("🔍 Testing empty name handling...")
        try:
            result = add_authorized_delivery_personnel("123456789", "", 7729984017)
            print(f"✅ Empty name handled: {result}")
        except Exception as e:
            print(f"✅ Empty name error handled: {type(e).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error scenario test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔐 PERSONNEL AUTHORIZATION WORKFLOW TEST")
    print("Testing the fix for 'save_data is not defined' error")
    print("=" * 70)
    
    workflow_success = test_personnel_workflow()
    error_success = test_error_scenarios()
    
    overall_success = workflow_success and error_success
    
    print(f"\n" + "=" * 70)
    print(f"📊 FINAL TEST RESULTS")
    print(f"✅ Workflow Test: {'PASSED' if workflow_success else 'FAILED'}")
    print(f"✅ Error Handling: {'PASSED' if error_success else 'FAILED'}")
    print(f"📈 Overall Success: {'100%' if overall_success else 'PARTIAL'}")
    
    if overall_success:
        print(f"\n🎉 CRITICAL AUTHORIZATION FIX VERIFIED!")
        print(f"\n📋 ISSUE RESOLUTION SUMMARY:")
        print(f"• ❌ Problem: 'save_data is not defined' error")
        print(f"• 🔧 Solution: Fixed all save_data() → set_data() calls")
        print(f"• ✅ Result: Personnel authorization now works perfectly")
        print(f"\n🚀 PRODUCTION IMPACT:")
        print(f"• New delivery personnel get immediate delivery bot access")
        print(f"• No system restart required for authorization changes")
        print(f"• Authorization synchronization is real-time")
        print(f"• Management bot → delivery bot workflow is seamless")
        
    else:
        print(f"\n⚠️ Some issues remain. Please review the test output above.")
    
    sys.exit(0 if overall_success else 1)
