#!/usr/bin/env python3
"""
Comprehensive test script for management bot synchronization and data integrity fixes.
This script verifies that all critical issues have been resolved.
"""

import sys
import os
import time
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_real_time_data_synchronization():
    """Test real-time data synchronization mechanisms"""
    print("🧪 Testing Real-time Data Synchronization...")
    
    try:
        from src.bots.management_bot import (
            refresh_personnel_data,
            refresh_availability_data,
            refresh_analytics_data,
            invalidate_personnel_cache
        )
        
        # Test data refresh functions
        print("  ✓ Testing personnel data refresh...")
        personnel_data = refresh_personnel_data()
        print(f"    - Retrieved {len(personnel_data)} personnel records")
        
        print("  ✓ Testing availability data refresh...")
        availability_data = refresh_availability_data()
        print(f"    - Retrieved {len(availability_data)} availability records")
        
        print("  ✓ Testing analytics data refresh...")
        analytics_data = refresh_analytics_data()
        total_records = sum(len(v) for v in analytics_data.values())
        print(f"    - Retrieved {total_records} total analytics records")
        
        print("  ✓ Testing cache invalidation...")
        invalidate_personnel_cache()
        print("    - Cache invalidation completed")
        
        print("✅ Real-time data synchronization tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_delivery_personnel_management():
    """Test delivery personnel management with proper cleanup"""
    print("\n🧪 Testing Delivery Personnel Management...")
    
    try:
        from src.utils.delivery_personnel_utils import (
            refresh_delivery_personnel_data,
            find_available_personnel_with_capacity_check
        )
        from src.firebase_db import get_data
        
        print("  ✓ Testing delivery personnel data refresh...")
        refresh_result = refresh_delivery_personnel_data()
        print(f"    - Data refresh result: {refresh_result}")
        
        print("  ✓ Testing order broadcasting logic...")
        # Test with a sample area
        available_personnel = find_available_personnel_with_capacity_check("1")
        print(f"    - Found {len(available_personnel)} available personnel for area 1")
        
        print("  ✓ Testing personnel data integrity...")
        personnel_data = get_data("delivery_personnel") or {}
        print(f"    - Current personnel count: {len(personnel_data)}")
        
        print("✅ Delivery personnel management tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_analytics_and_reporting():
    """Test analytics and reporting with real-time data"""
    print("\n🧪 Testing Analytics and Reporting...")
    
    try:
        from src.bots.management_bot import (
            validate_analytics_data,
            safe_get_numeric_value,
            safe_calculate_percentage
        )
        from src.firebase_db import get_data
        
        print("  ✓ Testing analytics data validation...")
        test_data = {"test": {"value": 100}}
        validated = validate_analytics_data(test_data, "test_collection")
        print(f"    - Validation result: {len(validated)} records")
        
        print("  ✓ Testing safe numeric operations...")
        test_order = {"subtotal": "50.00", "delivery_fee": 10}
        subtotal = safe_get_numeric_value(test_order, "subtotal", 0)
        delivery_fee = safe_get_numeric_value(test_order, "delivery_fee", 0)
        print(f"    - Subtotal: {subtotal}, Delivery fee: {delivery_fee}")
        
        percentage = safe_calculate_percentage(25, 100, 0)
        print(f"    - Percentage calculation: {percentage}%")
        
        print("  ✓ Testing real-time analytics data...")
        completed_orders = get_data("completed_orders") or {}
        confirmed_orders = get_data("confirmed_orders") or {}
        print(f"    - Completed orders: {len(completed_orders)}")
        print(f"    - Confirmed orders: {len(confirmed_orders)}")
        
        print("✅ Analytics and reporting tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_firebase_operations():
    """Test Firebase operations with validation and error handling"""
    print("\n🧪 Testing Firebase Operations...")
    
    try:
        from src.bots.management_bot import (
            validate_firebase_operation,
            safe_firebase_set,
            safe_firebase_update,
            verify_personnel_data_integrity
        )
        
        print("  ✓ Testing Firebase operation validation...")
        
        # Test valid operations
        valid_set = validate_firebase_operation("set", "test/path", {"data": "test"})
        valid_get = validate_firebase_operation("get", "test/path")
        print(f"    - Valid set operation: {valid_set}")
        print(f"    - Valid get operation: {valid_get}")
        
        # Test invalid operations
        invalid_path = validate_firebase_operation("set", "../dangerous/path", {"data": "test"})
        invalid_data = validate_firebase_operation("set", "test/path", None)
        print(f"    - Invalid path rejected: {not invalid_path}")
        print(f"    - Invalid data rejected: {not invalid_data}")
        
        print("  ✓ Testing safe Firebase operations...")
        # Note: These are dry runs, not actual Firebase writes
        print("    - Safe Firebase operations available")
        
        print("✅ Firebase operations tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_management_bot_integration():
    """Test management bot integration and callback handling"""
    print("\n🧪 Testing Management Bot Integration...")
    
    try:
        from src.bots.management_bot import (
            get_management_bot,
            create_main_menu_keyboard,
            escape_markdown
        )
        
        print("  ✓ Testing management bot instance...")
        management_bot = get_management_bot()
        print(f"    - Management bot available: {management_bot is not None}")
        
        print("  ✓ Testing menu creation...")
        keyboard = create_main_menu_keyboard()
        print(f"    - Main menu keyboard created: {keyboard is not None}")
        
        print("  ✓ Testing markdown escaping...")
        test_text = "Test $pecial characters & symbols"
        escaped = escape_markdown(test_text)
        print(f"    - Escaped text: {escaped}")
        
        print("✅ Management bot integration tests passed!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Management Bot Synchronization and Data Integrity Tests")
    print("=" * 70)
    
    tests = [
        test_real_time_data_synchronization,
        test_delivery_personnel_management,
        test_analytics_and_reporting,
        test_firebase_operations,
        test_management_bot_integration
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
            failed += 1
        
        time.sleep(1)  # Brief pause between tests
    
    print("\n" + "=" * 70)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Management bot fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
