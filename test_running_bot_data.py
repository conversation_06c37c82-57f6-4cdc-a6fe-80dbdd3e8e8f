#!/usr/bin/env python3
"""
Test the data in the currently running bots to see if they have the correct delivery personnel data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_current_bot_data():
    """Test the current data loaded in the bot system"""
    print("🔍 TESTING CURRENT BOT DATA")
    print("=" * 60)
    
    try:
        # Import the global data structures that the running bots use
        from src.data_models import (
            delivery_personnel, 
            delivery_personnel_availability, 
            delivery_personnel_capacity,
            delivery_personnel_zones
        )
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        print(f"📊 Current data loaded in bot system:")
        print(f"  delivery_personnel: {len(delivery_personnel)} records")
        print(f"  delivery_personnel_availability: {len(delivery_personnel_availability)} records")
        print(f"  delivery_personnel_capacity: {len(delivery_personnel_capacity)} records")
        print(f"  delivery_personnel_zones: {len(delivery_personnel_zones)} records")
        
        # Check target personnel
        target_personnel_id = "dp_31fe5be0"
        target_telegram_id = "1133538088"
        
        print(f"\n👤 Checking target personnel {target_personnel_id} ({target_telegram_id}):")
        
        # Check if personnel exists in each data structure
        personnel_exists = target_personnel_id in delivery_personnel
        availability_exists = target_personnel_id in delivery_personnel_availability
        capacity_exists = target_personnel_id in delivery_personnel_capacity
        zones_exists = target_personnel_id in delivery_personnel_zones
        
        print(f"  In delivery_personnel: {personnel_exists}")
        print(f"  In delivery_personnel_availability: {availability_exists}")
        print(f"  In delivery_personnel_capacity: {capacity_exists}")
        print(f"  In delivery_personnel_zones: {zones_exists}")
        
        if personnel_exists:
            personnel_data = delivery_personnel[target_personnel_id]
            print(f"\n📋 Personnel data:")
            print(f"  Name: {personnel_data.get('name')}")
            print(f"  Telegram ID: {personnel_data.get('telegram_id')}")
            print(f"  Status: {personnel_data.get('status')}")
            print(f"  Verified: {personnel_data.get('is_verified')}")
            print(f"  Service Areas: {personnel_data.get('service_areas')}")
            print(f"  Max Capacity: {personnel_data.get('max_capacity')}")
            print(f"  Current Capacity: {personnel_data.get('current_capacity')}")
        
        if availability_exists:
            availability_status = delivery_personnel_availability[target_personnel_id]
            print(f"\n📍 Availability status: {availability_status}")
        
        if capacity_exists:
            capacity_count = delivery_personnel_capacity[target_personnel_id]
            print(f"📦 Current capacity: {capacity_count}")
        
        if zones_exists:
            zones_list = delivery_personnel_zones[target_personnel_id]
            print(f"🗺️  Service zones: {zones_list}")
        
        # Test availability for different areas
        print(f"\n🔍 Testing find_available_personnel for different areas:")
        found_available = False
        
        for area_id in ['1', '2', '3', '4']:
            try:
                available_personnel = find_available_personnel(area_id)
                print(f"  Area {area_id}: {len(available_personnel)} personnel - {available_personnel}")
                
                if target_personnel_id in available_personnel:
                    print(f"    ✅ {target_personnel_id} is available for area {area_id}")
                    found_available = True
                else:
                    print(f"    ❌ {target_personnel_id} is NOT available for area {area_id}")
            except Exception as e:
                print(f"    ❌ Error testing area {area_id}: {e}")
        
        # If not found available, debug why
        if not found_available and personnel_exists:
            print(f"\n🔍 DEBUGGING WHY PERSONNEL IS NOT AVAILABLE:")
            
            from src.data_models import DeliveryPersonnel
            personnel = DeliveryPersonnel.from_dict(personnel_data)
            
            print(f"  personnel.is_available(): {personnel.is_available()}")
            print(f"    status == 'available': {personnel.status == 'available'}")
            print(f"    is_verified: {personnel.is_verified}")
            print(f"    current_capacity < max_capacity: {personnel.current_capacity < personnel.max_capacity}")
            
            print(f"  availability_status == 'available': {delivery_personnel_availability.get(target_personnel_id) == 'available'}")
            
            for area_id in ['1', '2', '3', '4']:
                can_serve = personnel.can_serve_area(area_id)
                print(f"  can_serve_area('{area_id}'): {can_serve}")
        
        return found_available
        
    except Exception as e:
        print(f"❌ Error testing bot data: {e}")
        import traceback
        traceback.print_exc()
        return False

def force_reload_data():
    """Force reload data from Firebase"""
    print(f"\n🔄 FORCE RELOADING DATA FROM FIREBASE")
    print("=" * 60)
    
    try:
        from src.data_storage import load_user_data
        from src.data_models import (
            delivery_personnel, 
            delivery_personnel_availability, 
            delivery_personnel_capacity,
            delivery_personnel_zones
        )
        
        print(f"📥 Loading fresh data from Firebase...")
        load_user_data()
        
        print(f"✅ Data reloaded successfully!")
        print(f"  delivery_personnel: {len(delivery_personnel)} records")
        print(f"  delivery_personnel_availability: {len(delivery_personnel_availability)} records")
        print(f"  delivery_personnel_capacity: {len(delivery_personnel_capacity)} records")
        print(f"  delivery_personnel_zones: {len(delivery_personnel_zones)} records")
        
        return True
        
    except Exception as e:
        print(f"❌ Error reloading data: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 TESTING RUNNING BOT DATA")
    print("=" * 80)
    
    # 1. Test current data
    print("1️⃣ Testing current data in running bot system...")
    current_available = test_current_bot_data()
    
    if not current_available:
        print(f"\n2️⃣ Personnel not available, forcing data reload...")
        reload_success = force_reload_data()
        
        if reload_success:
            print(f"\n3️⃣ Testing data after reload...")
            after_reload_available = test_current_bot_data()
            
            if after_reload_available:
                print(f"\n🎉 SUCCESS: Personnel is now available after data reload!")
            else:
                print(f"\n❌ ISSUE: Personnel still not available even after data reload")
        else:
            print(f"\n❌ FAILED: Could not reload data")
    else:
        print(f"\n🎉 SUCCESS: Personnel is already available in current bot data!")
    
    print(f"\n" + "=" * 80)
    print(f"🏁 TEST COMPLETE")
