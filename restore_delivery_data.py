#!/usr/bin/env python3
"""
Restore delivery locations and fees to Firebase Firestore.
This script will populate the missing delivery data that's causing order placement failures.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def restore_delivery_locations():
    """Restore delivery locations to Firebase"""
    print("📍 Restoring delivery locations to Firebase...")
    
    try:
        from src.firebase_db import set_data, get_data
        
        # Standard delivery locations based on the config
        delivery_locations = [
            {"id": 1, "name": "Applied Library"},
            {"id": 2, "name": "Federal Dorm"},
            {"id": 3, "name": "Anfi"},
            {"id": 4, "name": "Central Library"},
            {"id": 5, "name": "Masters Dorm (Lebs Matebiya)"},
            {"id": 6, "name": "B-371 (Fresh)"}
        ]
        
        delivery_locations_data = {"delivery_locations": delivery_locations}
        
        if set_data("delivery_locations", delivery_locations_data):
            print(f"✅ Successfully restored {len(delivery_locations)} delivery locations to Firebase")
            for location in delivery_locations:
                print(f"  - {location['name']} (ID: {location['id']})")
            return True
        else:
            print("❌ Failed to restore delivery locations to Firebase")
            return False
            
    except Exception as e:
        print(f"❌ Error restoring delivery locations: {e}")
        import traceback
        traceback.print_exc()
        return False

def restore_areas():
    """Restore areas to Firebase if missing"""
    print("\n🏢 Checking and restoring areas to Firebase...")
    
    try:
        from src.firebase_db import set_data, get_data
        
        # Check if areas exist
        areas_data = get_data("areas") or {"areas": []}
        existing_areas = areas_data.get("areas", [])
        
        if existing_areas:
            print(f"✅ Found {len(existing_areas)} existing areas in Firebase")
            for area in existing_areas:
                print(f"  - {area.get('name')} (ID: {area.get('id')})")
            return True
        
        # Standard areas based on the config
        areas = [
            {"id": 1, "name": "Bole Area"},
            {"id": 2, "name": "Geda Gate Area"},
            {"id": 3, "name": "Kereyu Area"},
            {"id": 4, "name": "College Mecheresha Area"},
            {"id": 5, "name": "Stadium Area"}
        ]
        
        areas_data = {"areas": areas}
        
        if set_data("areas", areas_data):
            print(f"✅ Successfully restored {len(areas)} areas to Firebase")
            for area in areas:
                print(f"  - {area['name']} (ID: {area['id']})")
            return True
        else:
            print("❌ Failed to restore areas to Firebase")
            return False
            
    except Exception as e:
        print(f"❌ Error restoring areas: {e}")
        import traceback
        traceback.print_exc()
        return False

def restore_delivery_fees():
    """Restore delivery fees to Firebase"""
    print("\n💰 Restoring delivery fees to Firebase...")
    
    try:
        from src.firebase_db import set_data, get_data
        
        # Delivery fees based on the original config
        delivery_fees = [
            # Bole Area (ID: 1)
            {"area_id": 1, "location_id": 1, "fee": 20},  # Applied Library
            {"area_id": 1, "location_id": 2, "fee": 20},  # Federal Dorm
            {"area_id": 1, "location_id": 3, "fee": 30},  # Anfi
            {"area_id": 1, "location_id": 4, "fee": 30},  # Central Library
            {"area_id": 1, "location_id": 5, "fee": 35},  # Masters Dorm
            {"area_id": 1, "location_id": 6, "fee": 40},  # B-371 (Fresh)
            
            # Geda Gate Area (ID: 2)
            {"area_id": 2, "location_id": 4, "fee": 20},  # Central Library
            {"area_id": 2, "location_id": 3, "fee": 25},  # Anfi
            {"area_id": 2, "location_id": 1, "fee": 30},  # Applied Library
            {"area_id": 2, "location_id": 2, "fee": 35},  # Federal Dorm
            {"area_id": 2, "location_id": 5, "fee": 30},  # Masters Dorm
            {"area_id": 2, "location_id": 6, "fee": 35},  # B-371 (Fresh)
            
            # Kereyu Area (ID: 3)
            {"area_id": 3, "location_id": 1, "fee": 25},  # Applied Library
            {"area_id": 3, "location_id": 2, "fee": 20},  # Federal Dorm
            {"area_id": 3, "location_id": 4, "fee": 40},  # Central Library
            {"area_id": 3, "location_id": 3, "fee": 40},  # Anfi
            {"area_id": 3, "location_id": 5, "fee": 40},  # Masters Dorm
            {"area_id": 3, "location_id": 6, "fee": 45},  # B-371 (Fresh)
            
            # College Mecheresha Area (ID: 4)
            {"area_id": 4, "location_id": 4, "fee": 30},  # Central Library
            {"area_id": 4, "location_id": 3, "fee": 35},  # Anfi
            {"area_id": 4, "location_id": 1, "fee": 40},  # Applied Library
            {"area_id": 4, "location_id": 2, "fee": 45},  # Federal Dorm
            {"area_id": 4, "location_id": 5, "fee": 30},  # Masters Dorm
            {"area_id": 4, "location_id": 6, "fee": 25},  # B-371 (Fresh)
            
            # Stadium Area (ID: 5) - This is the missing area causing the current issue
            {"area_id": 5, "location_id": 1, "fee": 30},  # Applied Library
            {"area_id": 5, "location_id": 2, "fee": 25},  # Federal Dorm
            {"area_id": 5, "location_id": 3, "fee": 35},  # Anfi
            {"area_id": 5, "location_id": 4, "fee": 35},  # Central Library
            {"area_id": 5, "location_id": 5, "fee": 40},  # Masters Dorm
            {"area_id": 5, "location_id": 6, "fee": 45},  # B-371 (Fresh)
        ]
        
        delivery_fees_data = {"delivery_fees": delivery_fees}
        
        if set_data("delivery_fees", delivery_fees_data):
            print(f"✅ Successfully restored {len(delivery_fees)} delivery fees to Firebase")
            
            # Group by area for display
            area_names = {1: "Bole Area", 2: "Geda Gate Area", 3: "Kereyu Area", 4: "College Mecheresha Area", 5: "Stadium Area"}
            location_names = {1: "Applied Library", 2: "Federal Dorm", 3: "Anfi", 4: "Central Library", 5: "Masters Dorm (Lebs Matebiya)", 6: "B-371 (Fresh)"}
            
            for area_id in sorted(area_names.keys()):
                area_name = area_names[area_id]
                print(f"\n  {area_name} (ID: {area_id}):")
                area_fees = [fee for fee in delivery_fees if fee["area_id"] == area_id]
                for fee in area_fees:
                    location_name = location_names.get(fee["location_id"], f"Location {fee['location_id']}")
                    print(f"    - {location_name}: {fee['fee']} birr")
            
            return True
        else:
            print("❌ Failed to restore delivery fees to Firebase")
            return False
            
    except Exception as e:
        print(f"❌ Error restoring delivery fees: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_data_restoration():
    """Verify that all data has been restored correctly"""
    print("\n✅ Verifying data restoration...")
    
    try:
        from src.firebase_db import get_data
        from src.utils.data_sync import validate_data_consistency, get_valid_delivery_locations_for_area
        
        # Check basic data counts
        areas_data = get_data("areas") or {"areas": []}
        delivery_locations_data = get_data("delivery_locations") or {"delivery_locations": []}
        delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}
        
        areas = areas_data.get("areas", [])
        delivery_locations = delivery_locations_data.get("delivery_locations", [])
        delivery_fees = delivery_fees_data.get("delivery_fees", [])
        
        print(f"📊 Data counts after restoration:")
        print(f"  Areas: {len(areas)}")
        print(f"  Delivery Locations: {len(delivery_locations)}")
        print(f"  Delivery Fees: {len(delivery_fees)}")
        
        # Test Stadium Area specifically (the problematic area)
        stadium_area_id = 5
        valid_locations = get_valid_delivery_locations_for_area(stadium_area_id)
        print(f"\n🏟️ Stadium Area (ID: {stadium_area_id}) validation:")
        print(f"  Valid delivery locations: {len(valid_locations)}")
        
        if valid_locations:
            print("  Available locations:")
            for location in valid_locations:
                print(f"    - {location['name']} (ID: {location['id']}): {location['fee']} birr")
            print("✅ Stadium Area now has valid delivery options!")
        else:
            print("❌ Stadium Area still has no valid delivery options")
            return False
        
        # Run full validation
        report = validate_data_consistency()
        print(f"\n📋 Full validation report:")
        print(f"  Total Issues: {report['summary']['total_issues']}")
        print(f"  Critical Issues: {report['summary']['critical_issues']}")
        
        if report['summary']['critical_issues'] == 0:
            print("✅ All critical issues resolved!")
            return True
        else:
            print(f"⚠️ {report['summary']['critical_issues']} critical issues remain")
            return False
            
    except Exception as e:
        print(f"❌ Error during verification: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to restore all delivery data"""
    print("🚀 Wiz-Aroma Delivery Data Restoration Tool")
    print("=" * 50)
    print("This script will restore missing delivery locations and fees to Firebase")
    print("to resolve the 'No valid delivery locations found' errors.\n")
    
    success_count = 0
    total_steps = 4
    
    # Step 1: Restore areas
    if restore_areas():
        success_count += 1
    
    # Step 2: Restore delivery locations
    if restore_delivery_locations():
        success_count += 1
    
    # Step 3: Restore delivery fees
    if restore_delivery_fees():
        success_count += 1
    
    # Step 4: Verify restoration
    if verify_data_restoration():
        success_count += 1
    
    # Summary
    print("\n" + "=" * 50)
    print("RESTORATION SUMMARY")
    print("=" * 50)
    print(f"Completed: {success_count}/{total_steps} steps")
    
    if success_count == total_steps:
        print("\n🎉 Data restoration completed successfully!")
        print("\n✅ The following issues should now be resolved:")
        print("  • 'No valid delivery locations found' errors")
        print("  • Stadium Area (area_id: 5) delivery options")
        print("  • All delivery locations properly restored")
        print("  • Delivery fees configured for all area-location combinations")
        print("\n📱 Users should now be able to:")
        print("  • Select restaurants from Stadium Area")
        print("  • See available delivery locations with fees")
        print("  • Complete orders without errors")
        print("\n🧪 Next steps:")
        print("  1. Test order placement from Stadium Area")
        print("  2. Verify all delivery locations show correct fees")
        print("  3. Run: python test_data_consistency_fix.py")
        return True
    else:
        print(f"\n❌ Data restoration incomplete ({success_count}/{total_steps} steps completed)")
        print("Some issues may remain. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
