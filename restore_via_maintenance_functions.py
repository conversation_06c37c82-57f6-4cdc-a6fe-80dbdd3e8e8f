#!/usr/bin/env python3
"""
Restore delivery data using maintenance bot functions directly.
This bypasses the bot interface and calls the functions directly.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def restore_delivery_locations_direct():
    """Restore delivery locations using maintenance functions"""
    print("📍 Restoring delivery locations using maintenance functions...")
    
    try:
        from src.data_storage import add_delivery_location, get_all_delivery_locations
        
        # Check existing locations first
        existing_locations = get_all_delivery_locations()
        print(f"Current delivery locations: {len(existing_locations)}")
        
        if existing_locations:
            print("Existing locations:")
            for loc in existing_locations:
                print(f"  - {loc['name']} (ID: {loc['id']})")
            
            # If we have locations, don't add duplicates
            location_names = {loc['name'] for loc in existing_locations}
        else:
            location_names = set()
        
        # Standard delivery locations
        standard_locations = [
            "Applied Library",
            "Federal Dorm", 
            "Anfi",
            "Central Library",
            "Masters Dorm (Lebs Matebiya)",
            "B-371 (Fresh)"
        ]
        
        added_count = 0
        for location_name in standard_locations:
            if location_name not in location_names:
                try:
                    new_location = add_delivery_location(location_name)
                    if new_location:
                        print(f"✅ Added: {location_name} (ID: {new_location['id']})")
                        added_count += 1
                    else:
                        print(f"❌ Failed to add: {location_name}")
                except Exception as e:
                    print(f"❌ Error adding {location_name}: {e}")
            else:
                print(f"⏭️ Skipped: {location_name} (already exists)")
        
        print(f"Added {added_count} new delivery locations")
        
        # Get updated list
        updated_locations = get_all_delivery_locations()
        print(f"Total delivery locations now: {len(updated_locations)}")
        
        return len(updated_locations) > 0
        
    except Exception as e:
        print(f"❌ Error restoring delivery locations: {e}")
        import traceback
        traceback.print_exc()
        return False

def restore_delivery_fees_direct():
    """Restore delivery fees using maintenance functions"""
    print("\n💰 Restoring delivery fees using maintenance functions...")
    
    try:
        from src.data_storage import add_delivery_fee, get_all_delivery_fees, get_all_areas, get_all_delivery_locations
        
        # Get current data
        areas = get_all_areas()
        locations = get_all_delivery_locations()
        existing_fees = get_all_delivery_fees()
        
        print(f"Areas: {len(areas)}, Locations: {len(locations)}, Existing fees: {len(existing_fees)}")
        
        if not areas:
            print("❌ No areas found. Cannot add delivery fees without areas.")
            return False
        
        if not locations:
            print("❌ No delivery locations found. Cannot add delivery fees without locations.")
            return False
        
        # Create area and location mappings
        area_map = {area['name']: area['id'] for area in areas}
        location_map = {loc['name']: loc['id'] for loc in locations}
        
        print("Available areas:", list(area_map.keys()))
        print("Available locations:", list(location_map.keys()))
        
        # Standard delivery fees configuration
        fee_config = {
            "Bole Area": {
                "Applied Library": 20,
                "Federal Dorm": 20,
                "Anfi": 30,
                "Central Library": 30,
                "Masters Dorm (Lebs Matebiya)": 35,
                "B-371 (Fresh)": 40
            },
            "Geda Gate Area": {
                "Central Library": 20,
                "Anfi": 25,
                "Applied Library": 30,
                "Federal Dorm": 35,
                "Masters Dorm (Lebs Matebiya)": 30,
                "B-371 (Fresh)": 35
            },
            "Kereyu Area": {
                "Applied Library": 25,
                "Federal Dorm": 20,
                "Central Library": 40,
                "Anfi": 40,
                "Masters Dorm (Lebs Matebiya)": 40,
                "B-371 (Fresh)": 45
            },
            "College Mecheresha Area": {
                "Central Library": 30,
                "Anfi": 35,
                "Applied Library": 40,
                "Federal Dorm": 45,
                "Masters Dorm (Lebs Matebiya)": 30,
                "B-371 (Fresh)": 25
            },
            "Stadium Area": {  # This is the critical missing area
                "Applied Library": 30,
                "Federal Dorm": 25,
                "Anfi": 35,
                "Central Library": 35,
                "Masters Dorm (Lebs Matebiya)": 40,
                "B-371 (Fresh)": 45
            }
        }
        
        added_count = 0
        updated_count = 0
        
        for area_name, location_fees in fee_config.items():
            if area_name not in area_map:
                print(f"⚠️ Area '{area_name}' not found in Firebase")
                continue
                
            area_id = area_map[area_name]
            print(f"\nProcessing {area_name} (ID: {area_id}):")
            
            for location_name, fee_amount in location_fees.items():
                if location_name not in location_map:
                    print(f"  ⚠️ Location '{location_name}' not found in Firebase")
                    continue
                
                location_id = location_map[location_name]
                
                try:
                    # Check if fee already exists
                    existing_fee = None
                    for fee in existing_fees:
                        if (int(fee.get('area_id', 0)) == area_id and 
                            int(fee.get('location_id', 0)) == location_id):
                            existing_fee = fee
                            break
                    
                    if existing_fee:
                        if existing_fee['fee'] != fee_amount:
                            # Update existing fee
                            result = add_delivery_fee(area_id, location_id, fee_amount)
                            if result:
                                print(f"  🔄 Updated: {location_name} - {existing_fee['fee']} → {fee_amount} birr")
                                updated_count += 1
                            else:
                                print(f"  ❌ Failed to update: {location_name}")
                        else:
                            print(f"  ✅ Exists: {location_name} - {fee_amount} birr")
                    else:
                        # Add new fee
                        result = add_delivery_fee(area_id, location_id, fee_amount)
                        if result:
                            print(f"  ➕ Added: {location_name} - {fee_amount} birr")
                            added_count += 1
                        else:
                            print(f"  ❌ Failed to add: {location_name}")
                            
                except Exception as e:
                    print(f"  ❌ Error processing {location_name}: {e}")
        
        print(f"\nDelivery fees restoration complete:")
        print(f"  Added: {added_count} new fees")
        print(f"  Updated: {updated_count} existing fees")
        
        return added_count > 0 or updated_count > 0
        
    except Exception as e:
        print(f"❌ Error restoring delivery fees: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_stadium_area():
    """Specifically verify Stadium Area has delivery options"""
    print("\n🏟️ Verifying Stadium Area delivery options...")
    
    try:
        from src.utils.data_sync import get_valid_delivery_locations_for_area
        from src.data_storage import get_all_areas
        
        # Find Stadium Area
        areas = get_all_areas()
        stadium_area = None
        for area in areas:
            if area['name'] == 'Stadium Area':
                stadium_area = area
                break
        
        if not stadium_area:
            print("❌ Stadium Area not found in Firebase")
            return False
        
        area_id = stadium_area['id']
        print(f"Stadium Area found with ID: {area_id}")
        
        # Get valid delivery locations
        valid_locations = get_valid_delivery_locations_for_area(area_id)
        
        print(f"Valid delivery locations for Stadium Area: {len(valid_locations)}")
        
        if valid_locations:
            print("Available delivery options:")
            for location in valid_locations:
                print(f"  - {location['name']} (ID: {location['id']}): {location['fee']} birr")
            print("✅ Stadium Area now has valid delivery options!")
            return True
        else:
            print("❌ Stadium Area still has no valid delivery options")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying Stadium Area: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main restoration function"""
    print("🚀 Direct Delivery Data Restoration")
    print("=" * 50)
    print("Using maintenance bot functions directly to restore missing data\n")
    
    success_count = 0
    total_steps = 3
    
    # Step 1: Restore delivery locations
    print("Step 1/3: Restore delivery locations")
    if restore_delivery_locations_direct():
        success_count += 1
        print("✅ Step 1 completed")
    else:
        print("❌ Step 1 failed")
    
    # Step 2: Restore delivery fees
    print("\nStep 2/3: Restore delivery fees")
    if restore_delivery_fees_direct():
        success_count += 1
        print("✅ Step 2 completed")
    else:
        print("❌ Step 2 failed")
    
    # Step 3: Verify Stadium Area specifically
    print("\nStep 3/3: Verify Stadium Area")
    if verify_stadium_area():
        success_count += 1
        print("✅ Step 3 completed")
    else:
        print("❌ Step 3 failed")
    
    # Summary
    print("\n" + "=" * 50)
    print("RESTORATION SUMMARY")
    print("=" * 50)
    print(f"Completed: {success_count}/{total_steps} steps")
    
    if success_count == total_steps:
        print("\n🎉 Delivery data restoration completed successfully!")
        print("\n✅ Issues resolved:")
        print("  • Delivery locations restored to Firebase")
        print("  • Delivery fees added for all area-location combinations")
        print("  • Stadium Area (area_id: 5) now has valid delivery options")
        print("  • 'No valid delivery locations found' errors should be fixed")
        print("\n📱 Users can now:")
        print("  • Select restaurants from Stadium Area")
        print("  • See available delivery locations with correct fees")
        print("  • Complete orders without location errors")
        print("\n🧪 Test the fix by:")
        print("  1. Placing an order from Stadium Area")
        print("  2. Verifying delivery locations appear with fees")
        print("  3. Completing the order successfully")
        return True
    else:
        print(f"\n❌ Restoration incomplete ({success_count}/{total_steps} steps completed)")
        print("Some issues may remain. Check the error messages above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
