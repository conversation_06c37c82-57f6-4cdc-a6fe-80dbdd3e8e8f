#!/usr/bin/env python3
"""
Test script to reproduce the Telegram API error with periods in MarkdownV2
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_markdown_formatting():
    """Test the exact formatting that's causing the error"""
    
    # Simulate the problematic text formatting
    test_cases = [
        # Case 1: Decimal numbers in earnings (likely the issue)
        "Daily: 25.50 birr - Weekly: 125.75 birr",
        
        # Case 2: Phone numbers with periods
        "Phone: +251.912.345.678",
        
        # Case 3: Mixed content with periods
        "1. <PERSON> - +251.912.345.678 - Daily: 25.50 birr - Weekly: 125.75 birr",
        
        # Case 4: The exact format from the code
        "1\\. <PERSON> \\- \\+251\\-912\\-345\\-678 \\- Daily: 25.50 birr \\- Weekly: 125.75 birr"
    ]
    
    print("🔍 Testing Markdown formatting issues...")
    print("=" * 60)
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        print(f"Original: {test_text}")
        
        # Try to identify unescaped periods
        unescaped_periods = []
        for j, char in enumerate(test_text):
            if char == '.' and (j == 0 or test_text[j-1] != '\\'):
                unescaped_periods.append(j)
        
        if unescaped_periods:
            print(f"❌ Found {len(unescaped_periods)} unescaped periods at positions: {unescaped_periods}")
            
            # Show the problematic characters
            for pos in unescaped_periods:
                start = max(0, pos - 5)
                end = min(len(test_text), pos + 6)
                context = test_text[start:end]
                print(f"   Position {pos}: ...{context}...")
        else:
            print("✅ No unescaped periods found")

def test_escape_function():
    """Test the escape_markdown function"""
    try:
        from bots.management_bot import escape_markdown
        
        print("\n🔧 Testing escape_markdown function...")
        print("=" * 60)
        
        test_values = [
            "25.50",
            "125.75",
            "+251.912.345.678",
            "John Doe",
            "Daily: 25.50 birr",
            "Weekly: 125.75 birr"
        ]
        
        for value in test_values:
            escaped = escape_markdown(value)
            print(f"Original: '{value}' -> Escaped: '{escaped}'")
            
    except ImportError as e:
        print(f"❌ Could not import escape_markdown: {e}")
        return False
    
    return True

def test_problematic_format():
    """Test the exact format that's causing the issue"""
    print("\n🎯 Testing the exact problematic format...")
    print("=" * 60)
    
    # Simulate the exact data that would cause the error
    personnel_data = {
        "person1": {
            "name": "John Doe",
            "phone_number": "+251-912-345-678"
        },
        "person2": {
            "name": "Jane Smith", 
            "phone_number": "+251.987.654.321"  # This has periods!
        }
    }
    
    earnings_data = {
        "person1": {"daily_earnings": 25.50, "weekly_earnings": 125.75},
        "person2": {"daily_earnings": 30.25, "weekly_earnings": 150.00}
    }
    
    try:
        from bots.management_bot import escape_markdown
        
        # Test the exact formatting from the code
        text = "✏️ *Edit Personnel*\n\n*Personnel List:*"
        
        personnel_list = list(personnel_data.items())
        for index, (personnel_id, person) in enumerate(personnel_list, 1):
            name = escape_markdown(person.get('name', 'Unknown'))
            phone = escape_markdown(person.get('phone_number', 'N/A'))
            
            earnings = earnings_data.get(personnel_id, {})
            daily_earnings = earnings.get('daily_earnings', 0.0)
            weekly_earnings = earnings.get('weekly_earnings', 0.0)
            
            # This is the problematic line - decimal numbers aren't escaped!
            line = f"\n{index}\\. {name} \\- {phone} \\- Daily: {daily_earnings:.2f} birr \\- Weekly: {weekly_earnings:.2f} birr"
            text += line
            
            print(f"Line {index}: {line}")
            
            # Check for unescaped periods in the decimal numbers
            if f"{daily_earnings:.2f}" in line and "." in f"{daily_earnings:.2f}":
                print(f"   ❌ Unescaped decimal in daily_earnings: {daily_earnings:.2f}")
            if f"{weekly_earnings:.2f}" in line and "." in f"{weekly_earnings:.2f}":
                print(f"   ❌ Unescaped decimal in weekly_earnings: {weekly_earnings:.2f}")
        
        print(f"\nFull text:\n{text}")
        
        # Count unescaped periods
        unescaped_count = 0
        for i, char in enumerate(text):
            if char == '.' and (i == 0 or text[i-1] != '\\'):
                unescaped_count += 1
        
        print(f"\n❌ Total unescaped periods: {unescaped_count}")
        
        return unescaped_count == 0
        
    except ImportError as e:
        print(f"❌ Could not import escape_markdown: {e}")
        return False

def main():
    """Run all tests"""
    print("🚨 TELEGRAM API ERROR REPRODUCTION TEST")
    print("Testing: 'can't parse entities: Character '.' is reserved and must be escaped'")
    print("=" * 80)
    
    test_markdown_formatting()
    escape_success = test_escape_function()
    format_success = test_problematic_format()
    
    print("\n" + "=" * 80)
    print("📋 SUMMARY:")
    print(f"✅ Escape function available: {escape_success}")
    print(f"❌ Format has unescaped periods: {not format_success}")
    
    if not format_success:
        print("\n🔧 SOLUTION NEEDED:")
        print("The decimal numbers in earnings display need to be escaped!")
        print("Change: f'{daily_earnings:.2f}' -> f'{daily_earnings:.2f}'.replace('.', '\\\\.')")
        print("Or use a helper function to escape the formatted numbers.")

if __name__ == "__main__":
    main()
