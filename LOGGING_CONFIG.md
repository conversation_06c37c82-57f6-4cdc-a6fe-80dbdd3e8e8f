# Logging Configuration Guide

## Overview

This document describes the logging configuration for the Wiz Aroma Delivery Bot. The logging system has been optimized to provide clear, actionable information while reducing noise and redundancy.

## Log Levels

The system uses the following log levels (from lowest to highest):

1. `TRACE` (5) - Custom level below DEBUG for very detailed diagnostic information
2. `DEBUG` (10) - Detailed information for debugging purposes
3. `INFO` (20) - General information about system operation
4. `WARNING` (30) - Information about potential issues
5. `ERROR` (40) - Error information that doesn't prevent the system from running
6. `CRITICAL` (50) - Critical errors that prevent the system from functioning

## Log Files

Logs are stored in the `logs/` directory with the following file structure:

- `bot_YYYY-MM-DD.log` - Contains all log messages (INFO and above by default)
- `errors_YYYY-MM-DD.log` - Contains only ERROR and CRITICAL level messages

## Log Filtering

The system includes a custom `LogFilter` class that filters out redundant information:

1. **Delivery Fee Checks**: Instead of logging every delivery fee check (which can generate hundreds of log messages), the system now only logs the final result.

2. **Redundant Data Messages**: Repetitive messages like database refresh notifications and large data dumps have been filtered to reduce log volume.

3. **Filter Patterns**: The system filters messages based on these patterns:
   - Restaurant menu data dumps
   - Frequent point balance retrievals
   - Recurring data refresh messages

## Structured Logging

The system uses structured logging methods that provide consistent formatting:

- `log_user_action` - For user actions (e.g., placing orders)
- `log_bot_action` - For bot actions (e.g., sending messages)
- `log_data_operation` - For database operations
- `log_api_call` - For external API calls
- `log_fee_summary` - For delivery fee determinations (replacing verbose logging)

## Summary Format

Data refresh operations now use a summary format that reduces log verbosity:

```
Refreshed user data from Firebase - Points: 61 users, Names: 3 users, Phone numbers: 3 users
```

Instead of multiple individual messages for each data type.

## Error Logging

Error logs include:
- Error message
- Exception type
- Stack trace
- File and line number

## Implementation

The logging system is implemented in `src/utils/logging_utils.py` and uses Python's built-in logging module with custom extensions.

## How to Enable/Disable Logging

To change the logging level for different components:

1. Edit `src/utils/logging_utils.py` and modify the `_setup_logger` method
2. Add or remove patterns in the `LogFilter` exclude_patterns list 