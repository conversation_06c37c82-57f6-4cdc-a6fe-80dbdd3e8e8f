#!/usr/bin/env python3
"""
Test with manual data loading
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_with_loading():
    """Test with manual data loading"""
    print("🚀 TEST WITH DATA LOADING")
    print("=" * 40)
    
    try:
        # Import required modules
        from src.data_models import delivery_personnel, delivery_personnel_availability, delivery_personnel_capacity, delivery_personnel_zones
        from src.data_storage import load_user_data
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        print("📊 Before loading:")
        print(f"  Personnel: {len(delivery_personnel)} records")
        print(f"  Availability: {len(delivery_personnel_availability)} records")
        print(f"  Capacity: {len(delivery_personnel_capacity)} records")
        print(f"  Zones: {len(delivery_personnel_zones)} records")
        
        print(f"\n📥 Loading data from Firebase...")
        load_user_data()
        
        print(f"\n📊 After loading:")
        print(f"  Personnel: {len(delivery_personnel)} records")
        print(f"  Availability: {len(delivery_personnel_availability)} records")
        print(f"  Capacity: {len(delivery_personnel_capacity)} records")
        print(f"  Zones: {len(delivery_personnel_zones)} records")
        
        # Check target personnel
        target_id = "dp_31fe5be0"
        if target_id in delivery_personnel:
            print(f"\n👤 Target personnel {target_id} found:")
            personnel_data = delivery_personnel[target_id]
            print(f"  Name: {personnel_data.get('name')}")
            print(f"  Status: {personnel_data.get('status')}")
            print(f"  Verified: {personnel_data.get('is_verified')}")
            print(f"  Service Areas: {personnel_data.get('service_areas')}")
            print(f"  Max Capacity: {personnel_data.get('max_capacity')}")
            print(f"  Current Capacity (from data): {personnel_data.get('current_capacity')}")
            print(f"  Availability: {delivery_personnel_availability.get(target_id)}")
            print(f"  Capacity (live): {delivery_personnel_capacity.get(target_id)}")
            print(f"  Zones: {delivery_personnel_zones.get(target_id)}")
        else:
            print(f"\n❌ Target personnel {target_id} NOT found")
        
        print(f"\n🔍 Testing availability for area 1:")
        available = find_available_personnel('1')
        print(f"  Found {len(available)} personnel: {available}")
        
        if target_id in available:
            print(f"  ✅ Target personnel {target_id} is available!")
        else:
            print(f"  ❌ Target personnel {target_id} is NOT available")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_with_loading()
