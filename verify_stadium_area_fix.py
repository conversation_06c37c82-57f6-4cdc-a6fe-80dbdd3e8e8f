#!/usr/bin/env python3
"""
Simple verification script to check if Stadium Area delivery options are working.
Run this after restoring delivery data to verify the fix.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def check_firebase_connection():
    """Check if we can connect to Firebase"""
    print("🔥 Checking Firebase connection...")
    
    try:
        from src.firebase_db import get_data
        
        # Try to get a simple data structure
        test_data = get_data("areas")
        if test_data is not None:
            print("✅ Firebase connection successful")
            return True
        else:
            print("⚠️ Firebase connection established but no data returned")
            return False
            
    except Exception as e:
        print(f"❌ Firebase connection failed: {e}")
        return False

def check_stadium_area_data():
    """Check if Stadium Area has the required data"""
    print("\n🏟️ Checking Stadium Area data...")
    
    try:
        from src.firebase_db import get_data
        
        # Check areas
        areas_data = get_data("areas") or {"areas": []}
        areas = areas_data.get("areas", [])
        
        stadium_area = None
        for area in areas:
            if area.get("name") == "Stadium Area":
                stadium_area = area
                break
        
        if not stadium_area:
            print("❌ Stadium Area not found in Firebase")
            return False
        
        area_id = stadium_area["id"]
        print(f"✅ Stadium Area found (ID: {area_id})")
        
        # Check delivery locations
        locations_data = get_data("delivery_locations") or {"delivery_locations": []}
        locations = locations_data.get("delivery_locations", [])
        
        print(f"📍 Found {len(locations)} delivery locations:")
        for location in locations:
            print(f"  - {location['name']} (ID: {location['id']})")
        
        if len(locations) == 0:
            print("❌ No delivery locations found")
            return False
        
        # Check delivery fees for Stadium Area
        fees_data = get_data("delivery_fees") or {"delivery_fees": []}
        fees = fees_data.get("delivery_fees", [])
        
        stadium_fees = []
        for fee in fees:
            if int(fee.get("area_id", 0)) == area_id:
                stadium_fees.append(fee)
        
        print(f"💰 Found {len(stadium_fees)} delivery fees for Stadium Area:")
        for fee in stadium_fees:
            location_id = fee["location_id"]
            fee_amount = fee["fee"]
            
            # Find location name
            location_name = "Unknown"
            for location in locations:
                if int(location["id"]) == int(location_id):
                    location_name = location["name"]
                    break
            
            print(f"  - {location_name} (ID: {location_id}): {fee_amount} birr")
        
        if len(stadium_fees) == 0:
            print("❌ No delivery fees found for Stadium Area")
            return False
        
        print("✅ Stadium Area has delivery fees configured")
        return True
        
    except Exception as e:
        print(f"❌ Error checking Stadium Area data: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_location_selection():
    """Test the delivery location selection logic"""
    print("\n🎯 Testing delivery location selection logic...")
    
    try:
        from src.utils.data_sync import get_valid_delivery_locations_for_area
        
        # Test Stadium Area specifically (area_id: 5)
        stadium_area_id = 5
        
        valid_locations = get_valid_delivery_locations_for_area(stadium_area_id)
        
        print(f"Valid delivery locations for Stadium Area: {len(valid_locations)}")
        
        if len(valid_locations) > 0:
            print("Available delivery options:")
            for location in valid_locations:
                print(f"  - {location['name']} (ID: {location['id']}): {location['fee']} birr")
            print("✅ Stadium Area delivery location selection working!")
            return True
        else:
            print("❌ No valid delivery locations found for Stadium Area")
            print("This means the 'No valid delivery locations found' error will still occur")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery location selection: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_gates_markup():
    """Test delivery gates markup generation"""
    print("\n🎛️ Testing delivery gates markup generation...")
    
    try:
        from src.utils.keyboards import get_delivery_gates_markup
        
        # Test Stadium Area
        area_name = "Stadium Area"
        area_id = 5
        
        print(f"Generating delivery gates markup for {area_name} (ID: {area_id})")
        
        markup = get_delivery_gates_markup(area_name, area_id)
        
        if markup and hasattr(markup, 'keyboard'):
            button_count = len(markup.keyboard)
            print(f"Generated markup with {button_count} buttons")
            
            if button_count > 1:  # More than just the cancel button
                print("Delivery location buttons:")
                for i, row in enumerate(markup.keyboard[:-1]):  # Exclude cancel button
                    for button in row:
                        print(f"  - {button.text}")
                print("✅ Delivery gates markup generation working!")
                return True
            else:
                print("❌ Only cancel button generated - no delivery locations available")
                return False
        else:
            print("❌ Failed to generate markup")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery gates markup: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main verification function"""
    print("🚀 Stadium Area Delivery Fix Verification")
    print("=" * 50)
    print("This script verifies that Stadium Area delivery options are working\n")
    
    tests = [
        ("Firebase Connection", check_firebase_connection),
        ("Stadium Area Data", check_stadium_area_data),
        ("Delivery Location Selection", test_delivery_location_selection),
        ("Delivery Gates Markup", test_delivery_gates_markup)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} - ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("VERIFICATION SUMMARY")
    print("=" * 50)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All verification tests passed!")
        print("\n✅ Stadium Area delivery options should now work:")
        print("  • Users can select restaurants from Stadium Area")
        print("  • Delivery locations appear with correct fees")
        print("  • Orders can be completed without errors")
        print("\n📱 Try placing a test order from Stadium Area to confirm!")
        return True
    else:
        print(f"\n💥 {total - passed} test(s) failed!")
        print("\n❌ Issues that need to be resolved:")
        
        failed_tests = [name for name, result in results if not result]
        for test_name in failed_tests:
            if test_name == "Firebase Connection":
                print("  • Check Firebase credentials and internet connection")
            elif test_name == "Stadium Area Data":
                print("  • Add Stadium Area delivery locations and fees via maintenance bot")
            elif test_name == "Delivery Location Selection":
                print("  • Ensure delivery fees are configured for Stadium Area")
            elif test_name == "Delivery Gates Markup":
                print("  • Verify data consistency between areas, locations, and fees")
        
        print("\n📋 Next steps:")
        print("  1. Follow the manual_data_restoration_guide.md")
        print("  2. Use maintenance bot to add missing data")
        print("  3. Run this script again to verify fixes")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
