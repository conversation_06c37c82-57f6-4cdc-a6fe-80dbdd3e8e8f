#!/usr/bin/env python3
"""
Test script to verify delivery fee calculation fixes in Wiz-Aroma ordering system.
This script tests the complete order lifecycle to ensure delivery fees are correctly
calculated and preserved from selection to Firebase storage.
"""

import sys
import os
import datetime
import logging

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def setup_logging():
    """Setup logging for the test"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    return logging.getLogger(__name__)

def test_delivery_fee_retrieval():
    """Test delivery fee retrieval from Firebase"""
    logger = setup_logging()
    logger.info("🧪 Testing delivery fee retrieval from Firebase...")
    
    try:
        from src.data_storage import get_delivery_fee, get_all_areas, get_all_delivery_locations
        
        # Get test data
        areas = get_all_areas()
        locations = get_all_delivery_locations()
        
        if not areas or not locations:
            logger.error("❌ No areas or locations found in database")
            return False
        
        # Test with first area and location
        test_area = areas[0]
        test_location = locations[0]
        area_id = test_area["id"]
        location_id = test_location["id"]
        
        logger.info(f"Testing with area_id={area_id} ({test_area['name']}) and location_id={location_id} ({test_location['name']})")
        
        # Test delivery fee retrieval
        fee = get_delivery_fee(area_id, location_id)
        logger.info(f"Retrieved delivery fee: {fee}")
        
        if fee > 0:
            logger.info("✅ Delivery fee retrieval test passed")
            return True
        else:
            logger.warning(f"⚠️ Delivery fee is {fee} - this might be expected if no fee is configured")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error testing delivery fee retrieval: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_data_structure():
    """Test order data structure and delivery fee preservation"""
    logger = setup_logging()
    logger.info("🧪 Testing order data structure and delivery fee preservation...")
    
    try:
        from src.data_storage import get_all_areas, get_all_delivery_locations, get_delivery_fee
        
        # Create a mock order structure
        areas = get_all_areas()
        locations = get_all_delivery_locations()
        
        if not areas or not locations:
            logger.error("❌ No areas or locations found for testing")
            return False
        
        test_area = areas[0]
        test_location = locations[0]
        area_id = test_area["id"]
        location_id = test_location["id"]
        
        # Test delivery fee calculation
        delivery_fee = get_delivery_fee(area_id, location_id)
        
        # Create mock order
        mock_order = {
            "user_id": 12345,
            "restaurant_id": 1,
            "restaurant_area_id": area_id,
            "delivery_gate": test_location["name"],
            "delivery_location_id": location_id,
            "delivery_fee": delivery_fee,
            "items": [
                {"name": "Test Item 1", "price": 50},
                {"name": "Test Item 2", "price": 75}
            ],
            "subtotal": 125,
            "delivery_name": "Test Customer",
            "phone_number": "+251912345678",
            "order_description": "Test order for delivery fee validation"
        }
        
        logger.info(f"Mock order created with delivery_fee={mock_order['delivery_fee']}")
        
        # Test order summary calculation
        subtotal = sum(item["price"] for item in mock_order["items"])
        order_delivery_fee = mock_order.get("delivery_fee", 0)
        total_price = subtotal + order_delivery_fee
        
        logger.info(f"Order calculation: subtotal={subtotal}, delivery_fee={order_delivery_fee}, total={total_price}")
        
        if order_delivery_fee >= 0:  # Allow 0 fees as they might be valid
            logger.info("✅ Order data structure test passed")
            return True
        else:
            logger.error(f"❌ Invalid delivery fee in order: {order_delivery_fee}")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing order data structure: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_firebase_order_storage():
    """Test Firebase order storage with delivery fees"""
    logger = setup_logging()
    logger.info("🧪 Testing Firebase order storage with delivery fees...")
    
    try:
        from src.firebase_db import set_data, get_data, delete_data
        from src.data_storage import get_all_areas, get_all_delivery_locations, get_delivery_fee
        
        # Create test order
        areas = get_all_areas()
        locations = get_all_delivery_locations()
        
        if not areas or not locations:
            logger.error("❌ No areas or locations found for Firebase test")
            return False
        
        test_area = areas[0]
        test_location = locations[0]
        area_id = test_area["id"]
        location_id = test_location["id"]
        delivery_fee = get_delivery_fee(area_id, location_id)
        
        test_order_number = f"TEST_DELIVERY_FEE_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        test_order_data = {
            "order_number": test_order_number,
            "user_id": 12345,
            "restaurant_id": 1,
            "restaurant_area_id": area_id,
            "delivery_gate": test_location["name"],
            "delivery_location_id": location_id,
            "delivery_fee": delivery_fee,
            "items": [{"name": "Test Item", "price": 100}],
            "subtotal": 100,
            "total": 100 + delivery_fee,
            "delivery_name": "Test Customer",
            "phone_number": "+251912345678",
            "status": "CONFIRMED",
            "delivery_status": "pending_assignment",
            "confirmed_at": datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        logger.info(f"Storing test order with delivery_fee={delivery_fee}")
        
        # Store in Firebase
        if set_data(f"confirmed_orders/{test_order_number}", test_order_data):
            logger.info("✅ Successfully stored test order in Firebase")
            
            # Retrieve and verify
            retrieved_order = get_data(f"confirmed_orders/{test_order_number}")
            if retrieved_order:
                retrieved_fee = retrieved_order.get("delivery_fee", -1)
                logger.info(f"Retrieved order with delivery_fee={retrieved_fee}")
                
                if retrieved_fee == delivery_fee:
                    logger.info("✅ Delivery fee preserved correctly in Firebase")
                    success = True
                else:
                    logger.error(f"❌ Delivery fee mismatch: stored={delivery_fee}, retrieved={retrieved_fee}")
                    success = False
                
                # Clean up test data
                delete_data(f"confirmed_orders/{test_order_number}")
                logger.info("🧹 Cleaned up test order from Firebase")
                
                return success
            else:
                logger.error("❌ Failed to retrieve test order from Firebase")
                return False
        else:
            logger.error("❌ Failed to store test order in Firebase")
            return False
            
    except Exception as e:
        logger.error(f"❌ Error testing Firebase order storage: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all delivery fee tests"""
    logger = setup_logging()
    logger.info("🚀 Starting delivery fee calculation fix tests...")
    
    tests = [
        ("Delivery Fee Retrieval", test_delivery_fee_retrieval),
        ("Order Data Structure", test_order_data_structure),
        ("Firebase Order Storage", test_firebase_order_storage)
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✅ {test_name} - PASSED")
            else:
                logger.error(f"❌ {test_name} - FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} - ERROR: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All delivery fee tests passed!")
        return True
    else:
        logger.error(f"💥 {total - passed} test(s) failed!")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
