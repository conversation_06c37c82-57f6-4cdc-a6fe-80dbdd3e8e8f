# Data Synchronization System

This document explains how the data synchronization system works between the Railway deployment and the GitHub repository.

## Overview

The Wiz Aroma Delivery System stores user data (like points, order history, etc.) in JSON files. When the system is deployed on Railway, these files are updated as users interact with the bot. However, these changes are not automatically reflected in the GitHub repository.

To solve this issue, we've implemented a data synchronization system that periodically fetches the latest data from the Railway deployment and updates the GitHub repository.

## How It Works

1. **Maintenance Bot Command**: We've added a `/export_data` command to the maintenance bot that exports all the current data as JSON files.

2. **GitHub Workflow**: A GitHub workflow runs on a schedule (every 6 hours) to:
   - Send the `/export_data` command to the maintenance bot
   - Download the exported data
   - Update the JSON files in the repository
   - Commit and push the changes

## Setup Instructions

To set up the data synchronization system, you need to:

1. **Add Repository Secrets**:
   - Go to your GitHub repository
   - Navigate to Settings > Secrets and variables > Actions
   - Add the following secrets:
     - `MAINTENANCE_BOT_TOKEN`: Your maintenance bot's token
     - `MAINTENANCE_CHAT_ID`: The chat ID of an authorized maintenance user

2. **Enable GitHub Workflow**:
   - The workflow is already defined in `.github/workflows/sync-data.yml`
   - It will run automatically every 6 hours
   - You can also trigger it manually from the Actions tab in your repository

## Manual Data Export

If you need to manually export the data:

1. Send the `/export_data` command to your maintenance bot
2. The bot will send you JSON files containing all the current data
3. You can download these files and manually update your repository

## Troubleshooting

If the data synchronization is not working:

1. **Check Permissions**: Make sure the maintenance bot token has the necessary permissions
2. **Check Chat ID**: Ensure the chat ID is correct and belongs to an authorized user
3. **Check Logs**: Review the GitHub Actions logs for any errors
4. **Manual Export**: Try manually exporting the data to see if the bot is working correctly

## Security Considerations

- The maintenance bot token is stored as a GitHub secret and is not exposed in the code
- Only authorized users can use the `/export_data` command
- The data is transferred securely via the Telegram Bot API

## Future Improvements

Potential improvements to the data synchronization system:

- Add email notifications for failed synchronizations
- Implement differential updates to only update changed files
- Add data validation to ensure the exported data is valid before updating the repository
