#!/usr/bin/env python
"""
Test script to verify the notification bot token.
"""

import telebot

# Test the notification bot token
NOTIFICATION_BOT_TOKEN = "8187447788:AAHh_MU3EPnyCz29aYbnPLERUUkPpxyGlrA"

def test_notification_bot():
    """Test the notification bot token"""
    try:
        bot = telebot.TeleBot(NOTIFICATION_BOT_TOKEN)
        bot_info = bot.get_me()
        print(f"✅ Notification bot connected successfully: @{bot_info.username}")
        print(f"Bot ID: {bot_info.id}")
        print(f"Bot Name: {bot_info.first_name}")
        return True
    except Exception as e:
        print(f"❌ Error connecting to notification bot: {e}")
        return False

if __name__ == "__main__":
    print("Testing notification bot token...")
    test_notification_bot()
