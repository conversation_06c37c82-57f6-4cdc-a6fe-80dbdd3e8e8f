#!/usr/bin/env python3
"""
Debug delivery location data to understand why it's showing "Location not specified"
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def debug_order_location_data():
    """Debug the actual location data stored in orders"""
    print("🔍 DEBUGGING ORDER LOCATION DATA")
    print("=" * 40)
    
    try:
        from src.firebase_db import get_data
        
        confirmed_orders = get_data("confirmed_orders") or {}
        
        if not confirmed_orders:
            print("📭 No confirmed orders found")
            return False
        
        print(f"📋 Found {len(confirmed_orders)} confirmed orders")
        print()
        
        for order_number, order_data in confirmed_orders.items():
            print(f"📋 Order #{order_number}:")
            
            # Check all possible location fields
            delivery_location = order_data.get('delivery_location')
            delivery_gate = order_data.get('delivery_gate')
            delivery_address = order_data.get('delivery_address')
            delivery_name = order_data.get('delivery_name')
            
            print(f"   • delivery_location: '{delivery_location}' (type: {type(delivery_location)})")
            print(f"   • delivery_gate: '{delivery_gate}' (type: {type(delivery_gate)})")
            print(f"   • delivery_address: '{delivery_address}' (type: {type(delivery_address)})")
            print(f"   • delivery_name: '{delivery_name}' (type: {type(delivery_name)})")
            
            # Check what our current formatting function would return
            print(f"   📍 Current formatting result:")
            
            # Test current logic
            if not delivery_location or delivery_location == 'N/A':
                print(f"      ❌ Would show: 'Location not specified' (delivery_location is '{delivery_location}')")
            else:
                formatted_location = delivery_location.strip()
                if delivery_gate and delivery_gate != 'N/A' and delivery_gate.strip():
                    gate_info = delivery_gate.strip()
                    if gate_info.lower() not in formatted_location.lower():
                        formatted_location = f"{formatted_location} ({gate_info})"
                print(f"      ✅ Would show: '{formatted_location}'")
            
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging location data: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_original_order_data():
    """Check if there's original order data that might have location info"""
    print("🔍 DEBUGGING ORIGINAL ORDER DATA")
    print("=" * 35)
    
    try:
        from src.firebase_db import get_data
        
        # Check if there are orders in other collections
        orders = get_data("orders") or {}
        awaiting_receipt = get_data("awaiting_receipt") or {}
        
        print(f"📋 Found {len(orders)} orders in 'orders' collection")
        print(f"📋 Found {len(awaiting_receipt)} orders in 'awaiting_receipt' collection")
        
        # Check orders collection
        if orders:
            print("\n📋 Sample order from 'orders' collection:")
            sample_order = list(orders.values())[0]
            for key, value in sample_order.items():
                if 'delivery' in key.lower() or 'location' in key.lower() or 'gate' in key.lower():
                    print(f"   • {key}: '{value}' (type: {type(value)})")
        
        # Check awaiting_receipt collection
        if awaiting_receipt:
            print("\n📋 Sample order from 'awaiting_receipt' collection:")
            sample_order = list(awaiting_receipt.values())[0]
            for key, value in sample_order.items():
                if 'delivery' in key.lower() or 'location' in key.lower() or 'gate' in key.lower():
                    print(f"   • {key}: '{value}' (type: {type(value)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error debugging original order data: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_location_formatting_functions():
    """Test the current location formatting functions"""
    print("🧪 TESTING LOCATION FORMATTING FUNCTIONS")
    print("=" * 45)
    
    try:
        # Test data scenarios
        test_cases = [
            {"delivery_location": "B-371", "delivery_gate": "Fresh", "expected": "B-371 (Fresh)"},
            {"delivery_location": "Building A", "delivery_gate": "Main Gate", "expected": "Building A (Main Gate)"},
            {"delivery_location": "N/A", "delivery_gate": "Fresh", "expected": "Location not specified"},
            {"delivery_location": None, "delivery_gate": "Fresh", "expected": "Location not specified"},
            {"delivery_location": "", "delivery_gate": "Fresh", "expected": "Location not specified"},
            {"delivery_location": "B-371", "delivery_gate": "N/A", "expected": "B-371"},
            {"delivery_location": "B-371", "delivery_gate": None, "expected": "B-371"},
            {"delivery_location": "B-371", "delivery_gate": "", "expected": "B-371"},
        ]
        
        print("🧪 Testing format_delivery_location_for_bot function:")
        
        for i, test_case in enumerate(test_cases, 1):
            delivery_location = test_case["delivery_location"]
            delivery_gate = test_case["delivery_gate"]
            expected = test_case["expected"]
            
            # Simulate the current function logic
            if not delivery_location or delivery_location == 'N/A':
                result = 'Location not specified'
            else:
                formatted_location = delivery_location.strip()
                if delivery_gate and delivery_gate != 'N/A' and delivery_gate.strip():
                    gate_info = delivery_gate.strip()
                    if gate_info.lower() not in formatted_location.lower():
                        formatted_location = f"{formatted_location} ({gate_info})"
                result = formatted_location
            
            status = "✅" if result == expected else "❌"
            print(f"   {status} Test {i}: location='{delivery_location}', gate='{delivery_gate}' → '{result}' (expected: '{expected}')")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing formatting functions: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_delivery_locations_data():
    """Check the delivery locations configuration data"""
    print("\n🔍 CHECKING DELIVERY LOCATIONS CONFIGURATION")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        
        delivery_locations = get_data("delivery_locations_data") or {}
        
        if delivery_locations:
            print(f"📋 Found delivery locations configuration:")
            locations = delivery_locations.get("delivery_locations", [])
            print(f"   • {len(locations)} delivery locations configured")
            
            if locations:
                print("\n📍 Sample delivery locations:")
                for i, location in enumerate(locations[:3], 1):
                    location_id = location.get("id")
                    location_name = location.get("name")
                    area_id = location.get("area_id")
                    print(f"   {i}. ID: {location_id}, Name: '{location_name}', Area: {area_id}")
        else:
            print("❌ No delivery locations configuration found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking delivery locations: {e}")
        import traceback
        traceback.print_exc()
        return False

def suggest_fixes():
    """Suggest fixes based on the debugging results"""
    print("\n💡 SUGGESTED FIXES")
    print("=" * 20)
    
    print("Based on the debugging results, here are the likely issues and fixes:")
    print()
    print("1. 🔍 **Check Field Names**:")
    print("   • Verify the correct field name for delivery location in orders")
    print("   • It might be 'delivery_gate' instead of 'delivery_location'")
    print("   • Or it could be stored in a different field name")
    print()
    print("2. 🔧 **Update Formatting Functions**:")
    print("   • Use the correct field name that actually contains the address")
    print("   • Add fallback logic to check multiple possible field names")
    print("   • Ensure proper null/empty checking")
    print()
    print("3. 📋 **Data Flow Issues**:")
    print("   • Check if location data is lost during order approval process")
    print("   • Verify that all location fields are copied to confirmed_orders")
    print("   • Ensure no data transformation is corrupting the location")
    print()
    print("4. 🧪 **Testing**:")
    print("   • Test with actual order data to verify fixes")
    print("   • Ensure both delivery bot and tracking bot show correct locations")

def main():
    """Main debug function"""
    print("🔍 DELIVERY LOCATION DEBUG TOOL")
    print("=" * 50)
    print("Investigating why delivery locations show 'Location not specified'")
    print()
    
    # Run all debug checks
    checks = [
        debug_order_location_data,
        debug_original_order_data,
        test_location_formatting_functions,
        check_delivery_locations_data
    ]
    
    results = []
    for check in checks:
        try:
            result = check()
            results.append(result)
            print()
        except Exception as e:
            print(f"❌ Check failed: {e}")
            results.append(False)
            print()
    
    # Show suggestions
    suggest_fixes()
    
    print("\n" + "=" * 50)
    successful_checks = sum(results)
    total_checks = len(results)
    
    print(f"📊 DEBUG SUMMARY: {successful_checks}/{total_checks} checks completed")
    
    if all(results):
        print("✅ All debug checks completed successfully")
        print("💡 Review the output above to identify the location data issue")
    else:
        print("❌ Some debug checks failed - review the errors above")
    
    return all(results)

if __name__ == "__main__":
    main()
