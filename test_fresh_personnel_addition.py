#!/usr/bin/env python3
"""
Test script to verify fresh delivery personnel addition via management bot
Tests the complete flow: Add Personnel → Authorization → Delivery Bot Access
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_add_personnel_programmatically():
    """Test adding personnel programmatically to simulate management bot process"""
    print("🧪 TESTING FRESH PERSONNEL ADDITION")
    print("=" * 50)
    
    try:
        from src.bots.management_bot import add_authorized_delivery_personnel
        from src.firebase_db import set_data, get_data
        from src.data_models import DeliveryPersonnel
        import uuid
        
        # Test personnel data
        test_personnel = [
            {
                "name": "<PERSON>",
                "phone": "+************",
                "telegram_id": "1234567890"
            },
            {
                "name": "<PERSON>", 
                "phone": "+************",
                "telegram_id": "9876543210"
            }
        ]
        
        added_personnel = []
        
        for i, person_data in enumerate(test_personnel, 1):
            print(f"\n👤 Adding Personnel {i}: {person_data['name']}")
            print("-" * 30)
            
            # Generate unique personnel ID
            personnel_id = f"dp_{uuid.uuid4().hex[:8]}"
            telegram_id = int(person_data['telegram_id'])
            
            print(f"📝 Personnel ID: {personnel_id}")
            print(f"📞 Phone: {person_data['phone']}")
            print(f"📱 Telegram ID: {telegram_id} (type: {type(telegram_id)})")
            
            # Create personnel object (like management bot does)
            personnel = DeliveryPersonnel(personnel_id)
            personnel.name = person_data['name']
            personnel.phone_number = person_data['phone']
            personnel.telegram_id = str(telegram_id)  # Store as string in Firebase
            personnel.service_areas = ['area_bole', 'area_4kilo', 'area_6kilo']
            personnel.vehicle_type = 'motorcycle'
            personnel.max_capacity = 5
            personnel.status = 'available'
            personnel.is_verified = True
            
            # Save to Firebase
            existing_personnel = get_data("delivery_personnel") or {}
            personnel_dict = personnel.to_dict()
            existing_personnel[personnel_id] = personnel_dict
            
            success = set_data("delivery_personnel", existing_personnel)
            
            if success:
                print(f"✅ Personnel saved to Firebase")
                
                # Add to authorized delivery personnel (like management bot does)
                admin_id = 7729984017  # Admin user
                auth_success = add_authorized_delivery_personnel(telegram_id, person_data['name'], admin_id)
                
                if auth_success:
                    print(f"✅ Added to authorized delivery personnel")
                    added_personnel.append((personnel_id, telegram_id, person_data['name']))
                else:
                    print(f"❌ Failed to add to authorized delivery personnel")
            else:
                print(f"❌ Failed to save personnel to Firebase")
        
        return added_personnel
        
    except Exception as e:
        print(f"❌ Error adding personnel: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_authorization_verification():
    """Test that authorization verification works correctly"""
    print("\n🔐 TESTING AUTHORIZATION VERIFICATION")
    print("=" * 50)
    
    try:
        from src.bots.delivery_bot import clear_authorization_cache, get_authorized_delivery_ids_from_firebase
        
        # Clear cache to force fresh data
        clear_authorization_cache()
        print("🔄 Authorization cache cleared")
        
        # Get fresh authorized IDs
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        print(f"📋 Fresh authorized IDs: {authorized_ids}")
        print(f"📊 All integers: {all(isinstance(x, int) for x in authorized_ids)}")
        
        # Test specific IDs
        test_ids = [1234567890, 9876543210]
        
        for test_id in test_ids:
            is_authorized = test_id in authorized_ids
            print(f"🎯 ID {test_id} authorized: {is_authorized}")
        
        return len(authorized_ids) > 1  # Should have admin + delivery personnel
        
    except Exception as e:
        print(f"❌ Authorization verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_personnel_lookup():
    """Test delivery bot personnel lookup functionality"""
    print("\n👤 TESTING PERSONNEL LOOKUP")
    print("=" * 50)
    
    try:
        from src.bots.delivery_bot import get_personnel_by_telegram_id
        
        test_ids = [1234567890, 9876543210]
        
        for test_id in test_ids:
            print(f"\n🔍 Looking up Telegram ID: {test_id}")
            personnel = get_personnel_by_telegram_id(test_id)
            
            if personnel:
                print(f"✅ Personnel found:")
                print(f"   Name: {personnel.name}")
                print(f"   Personnel ID: {personnel.personnel_id}")
                print(f"   Telegram ID: {personnel.telegram_id} (type: {type(personnel.telegram_id)})")
                print(f"   Status: {personnel.status}")
                print(f"   Verified: {personnel.is_verified}")
            else:
                print(f"❌ Personnel NOT found for Telegram ID {test_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Personnel lookup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_data_consistency():
    """Verify data consistency across all collections"""
    print("\n📊 VERIFYING DATA CONSISTENCY")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        
        # Check delivery_personnel collection
        personnel_data = get_data("delivery_personnel") or {}
        print(f"📋 delivery_personnel: {len(personnel_data)} records")
        
        # Check authorized_delivery_personnel collection
        auth_data = get_data("authorized_delivery_personnel") or {}
        print(f"📋 authorized_delivery_personnel: {len(auth_data)} records")
        
        # Verify consistency
        personnel_telegram_ids = set()
        for pid, pdata in personnel_data.items():
            tid = pdata.get('telegram_id')
            if tid:
                personnel_telegram_ids.add(int(tid))
                print(f"   👤 {pdata.get('name', 'Unknown')} (ID: {tid})")
        
        auth_telegram_ids = set()
        for aid, adata in auth_data.items():
            tid = adata.get('telegram_id')
            if tid:
                auth_telegram_ids.add(int(tid))
                print(f"   🔐 {adata.get('name', 'Unknown')} (ID: {tid})")
        
        # Check if they match
        if personnel_telegram_ids == auth_telegram_ids:
            print(f"✅ Data consistency verified: {len(personnel_telegram_ids)} personnel in both collections")
            return True
        else:
            print(f"❌ Data inconsistency detected:")
            print(f"   Personnel IDs: {personnel_telegram_ids}")
            print(f"   Auth IDs: {auth_telegram_ids}")
            return False
        
    except Exception as e:
        print(f"❌ Data consistency check failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run complete fresh personnel addition test"""
    print("🚀 FRESH DELIVERY PERSONNEL ADDITION TEST")
    print("=" * 60)
    
    # Step 1: Add personnel programmatically
    added_personnel = test_add_personnel_programmatically()
    step1_success = len(added_personnel) > 0
    
    # Step 2: Test authorization verification
    step2_success = test_authorization_verification()
    
    # Step 3: Test personnel lookup
    step3_success = test_personnel_lookup()
    
    # Step 4: Verify data consistency
    step4_success = verify_data_consistency()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    
    steps = [
        ("Add Personnel", step1_success),
        ("Authorization Verification", step2_success),
        ("Personnel Lookup", step3_success),
        ("Data Consistency", step4_success)
    ]
    
    passed = sum(success for _, success in steps)
    total = len(steps)
    
    for step_name, success in steps:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {step_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if all(success for _, success in steps):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Fresh personnel addition works correctly")
        print("✅ Authorization verification is working")
        print("✅ Data type consistency maintained")
        print("✅ System ready for production use")
        
        if added_personnel:
            print(f"\n📋 Added Personnel Summary:")
            for pid, tid, name in added_personnel:
                print(f"   👤 {name} (ID: {tid}, Personnel: {pid})")
        
        return True
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Please review errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
