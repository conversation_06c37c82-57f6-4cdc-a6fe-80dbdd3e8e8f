# Bot Tokens
BOT_TOKEN=your_bot_token_here
ADMIN_BOT_TOKEN=your_admin_bot_token_here
FINANCE_BOT_TOKEN=your_finance_bot_token_here
MAINTENANCE_BOT_TOKEN=your_maintenance_bot_token_here
MANAGEMENT_BOT_TOKEN=your_management_bot_token_here

# Chat IDs
ADMIN_CHAT_IDS="[your_admin_id_here]"
FINANCE_CHAT_ID=your_finance_id_here
MAINTENANCE_CHAT_ID=your_maintenance_id_here
MANAGEMENT_CHAT_ID=your_management_id_here

# Email Configuration
EMAIL_ADDRESS=*******
EMAIL_PASSWORD=your_email_password_here

# Payment Information
# Telebirr
TELEBIRR_PHONE=telebirr_phone_number
TELEBIRR_NAME=telebirr_account_name

# CBE Bank
CBE_ACCOUNT_NUMBER=cbe_account_number
CBE_ACCOUNT_NAME=cbe_account_name

# BOA Bank
BOA_ACCOUNT_NUMBER=boa_account_number
BOA_ACCOUNT_NAME=boa_account_name

# Contact Information
SUPPORT_PHONE_1=support_phone_1
SUPPORT_PHONE_2=support_phone_2
SUPPORT_TELEGRAM=support_telegram_handle

# Firebase Configuration
# Firebase database URL (required for Firebase integration)
FIREBASE_DATABASE_URL=https://your-project-id-default-rtdb.firebaseio.com/

# Firebase credentials (choose one method):
# Method 1: JSON credentials as environment variable (recommended for production)
# FIREBASE_CREDENTIALS={"type":"service_account","project_id":"your-project-id","private_key_id":"your-private-key-id","private_key":"-----BEGIN PRIVATE KEY-----\nYOUR_PRIVATE_KEY\n-----END PRIVATE KEY-----\n","client_email":"*******","client_id":"your-client-id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"https://www.googleapis.com/robot/v1/metadata/x509/firebase-adminsdk-xxxxx%40your-project-id.iam.gserviceaccount.com"}

# Method 2: Path to Firebase credentials JSON file (for local development)
# FIREBASE_CREDENTIALS_PATH=firebase-credentials.json

# Application Configuration
# Test mode (set to False for production, True for development/testing)
TEST_MODE=False

# Logging configuration
LOG_LEVEL=INFO