#!/usr/bin/env python3
"""
Comprehensive test script to verify all delivery system fixes
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data
from src.data_models import DeliveryPersonnel
from src.utils.delivery_personnel_utils import find_available_personnel
from src.data_storage import load_user_data
import json

def test_delivery_personnel_data():
    """Test delivery personnel data loading and availability"""
    print("🔍 TESTING DELIVERY PERSONNEL DATA")
    print("=" * 60)
    
    try:
        # Load fresh data from Firebase
        print("📥 Loading fresh data from Firebase...")
        load_user_data()
        
        # Get data from Firebase directly
        personnel = get_data('delivery_personnel') or {}
        availability = get_data('delivery_personnel_availability') or {}
        capacity = get_data('delivery_personnel_capacity') or {}
        zones = get_data('delivery_personnel_zones') or {}
        
        print(f"\n📊 DATA SUMMARY:")
        print(f"  Total personnel: {len(personnel)}")
        print(f"  Total availability records: {len(availability)}")
        print(f"  Total capacity records: {len(capacity)}")
        print(f"  Total zones records: {len(zones)}")
        
        # Check target personnel
        target_found = False
        target_personnel_id = None
        
        print(f"\n👥 PERSONNEL DETAILS:")
        for pid, pdata in personnel.items():
            telegram_id = pdata.get('telegram_id')
            name = pdata.get('name')
            status = pdata.get('status')
            is_verified = pdata.get('is_verified')
            service_areas = pdata.get('service_areas', [])
            
            print(f"\n  Personnel {pid}:")
            print(f"    Name: {name}")
            print(f"    Telegram ID: {telegram_id}")
            print(f"    Status: {status}")
            print(f"    Verified: {is_verified}")
            print(f"    Service Areas: {service_areas}")
            print(f"    Availability: {availability.get(pid, 'NOT_SET')}")
            print(f"    Capacity: {capacity.get(pid, 'NOT_SET')}")
            print(f"    Zones: {zones.get(pid, 'NOT_SET')}")
            
            if telegram_id == '1133538088':
                target_found = True
                target_personnel_id = pid
                print(f"    🎯 TARGET PERSONNEL FOUND!")
        
        if target_found:
            print(f"\n✅ Target personnel (1133538088) found with ID: {target_personnel_id}")
        else:
            print(f"\n❌ Target personnel (1133538088) NOT found!")
            
        return target_found, target_personnel_id
        
    except Exception as e:
        print(f"❌ Error testing delivery personnel data: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_availability_filtering():
    """Test the find_available_personnel function"""
    print("\n🔍 TESTING AVAILABILITY FILTERING")
    print("=" * 60)
    
    try:
        # Test for different areas
        test_areas = [1, 2, 3, 4]
        
        for area_id in test_areas:
            print(f"\n📍 Testing area {area_id}:")
            available_personnel = find_available_personnel(area_id)
            print(f"  Available personnel: {available_personnel}")
            print(f"  Count: {len(available_personnel)}")
            
            # Check if target personnel is included
            if '1133538088' in str(available_personnel):
                print(f"  🎯 Target personnel (1133538088) INCLUDED!")
            else:
                print(f"  ❌ Target personnel (1133538088) NOT included")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing availability filtering: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_confirmation_fix():
    """Test the customer confirmation workflow fix"""
    print("\n🔍 TESTING CUSTOMER CONFIRMATION FIX")
    print("=" * 60)
    
    try:
        # Test order number parsing
        test_order_number = "7729984017_2507020014_0001"
        
        # Extract user ID from order number (same logic as in the fix)
        try:
            customer_user_id = test_order_number.split('_')[0]
            print(f"✅ Successfully extracted user ID from order number:")
            print(f"  Order Number: {test_order_number}")
            print(f"  Extracted User ID: {customer_user_id}")
            
            if customer_user_id == "7729984017":
                print(f"  ✅ Correct user ID extracted!")
                return True
            else:
                print(f"  ❌ Incorrect user ID extracted!")
                return False
                
        except (IndexError, ValueError) as e:
            print(f"❌ Failed to extract user ID: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing customer confirmation fix: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_bot_connections():
    """Test bot connections and imports"""
    print("\n🔍 TESTING BOT CONNECTIONS")
    print("=" * 60)
    
    try:
        # Test imports
        from src.bots.order_track_bot import send_customer_confirmation_request
        from src.bot_instance import bot
        from src.handlers.order_handlers import handle_delivery_confirmation
        
        print("✅ All imports successful")
        
        # Test bot connection
        try:
            bot_info = bot.get_me()
            print(f"✅ User bot connected: @{bot_info.username}")
        except Exception as bot_error:
            print(f"❌ User bot connection failed: {bot_error}")
            return False
        
        # Test function signature
        import inspect
        sig = inspect.signature(send_customer_confirmation_request)
        params = list(sig.parameters.keys())
        
        if 'order_number' in params:
            print("✅ send_customer_confirmation_request has correct signature")
        else:
            print(f"❌ Unexpected signature: {params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing bot connections: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_test_summary(personnel_test, availability_test, confirmation_test, bot_test):
    """Generate comprehensive test summary"""
    print("\n" + "=" * 80)
    print("🎯 COMPREHENSIVE TEST SUMMARY")
    print("=" * 80)
    
    total_tests = 4
    passed_tests = sum([personnel_test, availability_test, confirmation_test, bot_test])
    
    print(f"\n📊 OVERALL RESULTS:")
    print(f"  Tests Passed: {passed_tests}/{total_tests}")
    print(f"  Success Rate: {(passed_tests/total_tests)*100:.1f}%")
    
    print(f"\n📋 DETAILED RESULTS:")
    print(f"  ✅ Delivery Personnel Data: {'PASS' if personnel_test else 'FAIL'}")
    print(f"  ✅ Availability Filtering: {'PASS' if availability_test else 'FAIL'}")
    print(f"  ✅ Customer Confirmation Fix: {'PASS' if confirmation_test else 'FAIL'}")
    print(f"  ✅ Bot Connections: {'PASS' if bot_test else 'FAIL'}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 ALL TESTS PASSED! System is ready for production testing.")
        print(f"\n🚀 NEXT STEPS:")
        print(f"  1. Place a real order to test the complete workflow")
        print(f"  2. Verify that personnel 1133538088 receives delivery notifications")
        print(f"  3. Test the complete order lifecycle with customer confirmation")
        print(f"  4. Monitor logs for any remaining issues")
    else:
        print(f"\n❌ SOME TESTS FAILED - Manual intervention required")
        
        if not personnel_test:
            print(f"  🔧 Fix delivery personnel data in Firebase")
        if not availability_test:
            print(f"  🔧 Debug find_available_personnel function")
        if not confirmation_test:
            print(f"  🔧 Fix customer confirmation workflow")
        if not bot_test:
            print(f"  🔧 Fix bot connections and imports")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    print("🚀 COMPREHENSIVE DELIVERY SYSTEM FIXES TEST")
    print("=" * 80)
    
    # Run all tests
    personnel_test, target_id = test_delivery_personnel_data()
    availability_test = test_availability_filtering()
    confirmation_test = test_customer_confirmation_fix()
    bot_test = test_bot_connections()
    
    # Generate summary
    all_passed = generate_test_summary(personnel_test, availability_test, confirmation_test, bot_test)
    
    if all_passed:
        print(f"\n🎯 READY FOR REAL ORDER TEST!")
        print(f"Target personnel ID: {target_id}")
    else:
        print(f"\n❌ FIXES NEEDED BEFORE TESTING")
