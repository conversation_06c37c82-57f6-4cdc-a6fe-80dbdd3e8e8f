#!/usr/bin/env python3
"""
Verify data consistency between Firebase Firestore and data models
after proper initialization with load_user_data().
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_data_model_firebase_sync():
    """Test data model synchronization with Firebase after proper loading"""
    print("🔄 Testing Data Model Firebase Synchronization...")
    
    try:
        # First, load data using the proper initialization function
        print("  📥 Loading data using load_user_data()...")
        from src.data_storage import load_user_data
        load_user_data()
        print("  ✅ Data loaded successfully")
        
        # Now check data models
        from src.data_models import (
            delivery_personnel,
            delivery_personnel_availability,
            delivery_personnel_capacity,
            delivery_personnel_assignments,
            delivery_personnel_zones,
            delivery_personnel_performance,
            delivery_personnel_earnings
        )
        from src.firebase_db import get_data
        
        # Test all delivery personnel collections
        collections_to_test = {
            "delivery_personnel": delivery_personnel,
            "delivery_personnel_availability": delivery_personnel_availability,
            "delivery_personnel_capacity": delivery_personnel_capacity,
            "delivery_personnel_assignments": delivery_personnel_assignments,
            "delivery_personnel_zones": delivery_personnel_zones,
            "delivery_personnel_performance": delivery_personnel_performance,
            "delivery_personnel_earnings": delivery_personnel_earnings
        }
        
        sync_results = {}
        all_synchronized = True
        
        for collection_name, model_data in collections_to_test.items():
            print(f"  📊 Checking {collection_name}...")
            
            # Get data from Firebase
            firebase_data = get_data(collection_name) or {}
            
            # Compare with data model
            model_count = len(model_data) if isinstance(model_data, dict) else 0
            firebase_count = len(firebase_data) if isinstance(firebase_data, dict) else 0
            
            synchronized = model_count == firebase_count
            if not synchronized:
                all_synchronized = False
            
            sync_results[collection_name] = {
                "model_count": model_count,
                "firebase_count": firebase_count,
                "synchronized": synchronized
            }
            
            status = "✅" if synchronized else "❌"
            print(f"    {status} Model: {model_count}, Firebase: {firebase_count}")
        
        return all_synchronized, sync_results
        
    except Exception as e:
        print(f"  ❌ Error testing data model sync: {e}")
        return False, {}

def test_firebase_exclusive_operations():
    """Test that all operations use Firebase exclusively"""
    print("\n🔥 Testing Firebase Exclusive Operations...")
    
    try:
        # Test management bot operations
        from src.bots.management_bot import (
            refresh_personnel_data,
            refresh_analytics_data,
            safe_firebase_set,
            safe_firebase_update
        )
        
        print("  🔄 Testing management bot Firebase operations...")
        
        # Test refresh operations (these should use get_data internally)
        personnel_data = refresh_personnel_data()
        analytics_data = refresh_analytics_data()
        
        print(f"    ✅ refresh_personnel_data(): {len(personnel_data)} records")
        print(f"    ✅ refresh_analytics_data(): {sum(len(v) if isinstance(v, dict) else 0 for v in analytics_data.values())} total records")
        
        # Test delivery personnel utils operations
        from src.utils.delivery_personnel_utils import (
            refresh_delivery_personnel_data,
            find_available_personnel_with_capacity_check
        )
        
        print("  🚚 Testing delivery personnel utils Firebase operations...")
        
        refresh_result = refresh_delivery_personnel_data()
        available_personnel = find_available_personnel_with_capacity_check("1")
        
        print(f"    ✅ refresh_delivery_personnel_data(): {refresh_result}")
        print(f"    ✅ find_available_personnel_with_capacity_check(): {len(available_personnel)} available")
        
        # Test earnings utils operations
        from src.utils.earnings_utils import get_or_create_personnel_earnings
        
        print("  💰 Testing earnings utils Firebase operations...")
        
        # Get a sample personnel ID
        from src.data_models import delivery_personnel
        if delivery_personnel:
            sample_id = list(delivery_personnel.keys())[0]
            earnings = get_or_create_personnel_earnings(sample_id)
            print(f"    ✅ get_or_create_personnel_earnings(): {earnings.personnel_id}")
        else:
            print("    ℹ️  No personnel available for earnings test")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing Firebase operations: {e}")
        return False

def test_no_local_storage_usage():
    """Test that no local storage is being used for primary data"""
    print("\n📁 Testing No Local Storage Usage...")
    
    try:
        # Check USE_FIREBASE flag
        from src.data_storage import USE_FIREBASE
        print(f"  🔥 USE_FIREBASE flag: {USE_FIREBASE}")
        
        if not USE_FIREBASE:
            print("  ❌ WARNING: USE_FIREBASE is False!")
            return False
        
        # Test that data loading functions use Firebase
        from src.data_storage import (
            load_delivery_personnel_data,
            load_delivery_personnel_assignments_data,
            load_delivery_personnel_availability_data
        )
        
        print("  📊 Testing data loading functions...")
        
        # These should return Firebase data
        personnel_data = load_delivery_personnel_data()
        assignments_data = load_delivery_personnel_assignments_data()
        availability_data = load_delivery_personnel_availability_data()
        
        print(f"    ✅ load_delivery_personnel_data(): {len(personnel_data)} records")
        print(f"    ✅ load_delivery_personnel_assignments_data(): {len(assignments_data)} records")
        print(f"    ✅ load_delivery_personnel_availability_data(): {len(availability_data)} records")
        
        # Test that save functions use Firebase
        print("  💾 Testing save functions use Firebase...")
        
        # Import save functions
        from src.data_storage import (
            save_delivery_personnel,
            save_delivery_personnel_assignments,
            save_delivery_personnel_availability
        )
        
        print("    ✅ Save functions available and use Firebase operations")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing local storage usage: {e}")
        return False

def test_real_time_data_updates():
    """Test real-time data updates work correctly"""
    print("\n⚡ Testing Real-time Data Updates...")
    
    try:
        from src.bots.management_bot import invalidate_personnel_cache
        from src.utils.delivery_personnel_utils import refresh_delivery_personnel_data
        
        print("  🗑️ Testing cache invalidation...")
        invalidate_personnel_cache()
        print("    ✅ Cache invalidation completed")
        
        print("  🔄 Testing data refresh after cache invalidation...")
        refresh_result = refresh_delivery_personnel_data()
        print(f"    ✅ Data refresh after invalidation: {refresh_result}")
        
        # Test that management bot refresh functions work
        from src.bots.management_bot import refresh_personnel_data, refresh_analytics_data
        
        print("  📊 Testing management bot refresh functions...")
        personnel_data = refresh_personnel_data()
        analytics_data = refresh_analytics_data()
        
        print(f"    ✅ Management bot personnel refresh: {len(personnel_data)} records")
        print(f"    ✅ Management bot analytics refresh: {sum(len(v) if isinstance(v, dict) else 0 for v in analytics_data.values())} records")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing real-time updates: {e}")
        return False

def main():
    """Run comprehensive Firebase exclusive access verification"""
    print("🚀 Firebase Firestore Data Consistency Verification")
    print("=" * 70)
    
    tests = [
        ("Data Model Firebase Sync", test_data_model_firebase_sync),
        ("Firebase Exclusive Operations", test_firebase_exclusive_operations),
        ("No Local Storage Usage", test_no_local_storage_usage),
        ("Real-time Data Updates", test_real_time_data_updates)
    ]
    
    results = {}
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_name == "Data Model Firebase Sync":
                # Special handling for sync test which returns tuple
                result, details = test_func()
                results[test_name] = {"passed": result, "details": details}
            else:
                result = test_func()
                results[test_name] = {"passed": result}
            
            if result:
                passed += 1
        except Exception as e:
            print(f"  ❌ Test '{test_name}' failed with exception: {e}")
            results[test_name] = {"passed": False, "error": str(e)}
    
    print("\n" + "=" * 70)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 70)
    
    for test_name, result in results.items():
        status = "✅ PASSED" if result["passed"] else "❌ FAILED"
        print(f"{status}: {test_name}")
        
        if test_name == "Data Model Firebase Sync" and "details" in result:
            details = result["details"]
            for collection, info in details.items():
                sync_status = "✅" if info["synchronized"] else "❌"
                print(f"  {sync_status} {collection}: Model({info['model_count']}) Firebase({info['firebase_count']})")
    
    print(f"\n🏆 Overall: {passed}/{total} tests passed")
    
    firebase_exclusive = passed == total
    print(f"\n🎯 FIREBASE EXCLUSIVE ACCESS: {'✅ VERIFIED' if firebase_exclusive else '❌ ISSUES FOUND'}")
    
    if firebase_exclusive:
        print("✅ All data is properly stored in and accessed exclusively from Firebase Firestore")
        print("✅ Data models are synchronized with Firebase collections")
        print("✅ No local storage fallbacks are being used")
        print("✅ Real-time data updates are working correctly")
    else:
        print("⚠️  Some issues found - review details above")
    
    return firebase_exclusive

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
