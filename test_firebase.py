#!/usr/bin/env python3
"""
Firebase Connectivity Test Script for Wiz-Aroma V-1.3.3

This script tests Firebase connectivity and JWT token validity
before starting the main bot system.
"""

import os
import sys
import logging
from datetime import datetime

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def main():
    """Main function to test Firebase connectivity"""
    print("🔥 Firebase Connectivity Test for Wiz-Aroma V-1.3.3")
    print("=" * 60)
    
    try:
        # Load environment variables
        from dotenv import load_dotenv
        load_dotenv()
        
        print("✅ Environment variables loaded")
        
        # Check if Firebase is enabled
        use_firebase = os.getenv('USE_FIREBASE', 'false').lower() == 'true'
        if not use_firebase:
            print("❌ Firebase is disabled in environment variables")
            return False
            
        print("✅ Firebase is enabled in environment")
        
        # Check credentials file
        credentials_path = os.getenv('FIREBASE_CREDENTIALS_PATH', 'wiz-aroma-adama-firebase-adminsdk-fbsvc-c2564abcb8.json')
        if not os.path.exists(credentials_path):
            print(f"❌ Firebase credentials file not found: {credentials_path}")
            return False
            
        print(f"✅ Firebase credentials file found: {credentials_path}")
        
        # Check database URL
        database_url = os.getenv('FIREBASE_DATABASE_URL')
        if not database_url:
            print("❌ Firebase database URL not found in environment")
            return False
            
        print(f"✅ Firebase database URL configured: {database_url}")
        
        # Test Firebase initialization
        print("\n🔧 Testing Firebase initialization...")
        from src.firebase_db import initialize_firebase
        
        if initialize_firebase():
            print("✅ Firebase initialized successfully")
        else:
            print("❌ Firebase initialization failed")
            return False
        
        # Test Firebase connectivity
        print("\n🌐 Testing Firebase connectivity...")
        from src.firebase_db import test_firebase_connectivity
        
        if test_firebase_connectivity():
            print("✅ Firebase connectivity test PASSED")
        else:
            print("❌ Firebase connectivity test FAILED")
            return False
        
        # Test basic operations
        print("\n📊 Testing basic Firebase operations...")
        from src.firebase_db import get_data, set_data
        
        # Test write operation
        test_path = "/connectivity_test"
        test_data = {
            "timestamp": datetime.now().isoformat(),
            "test_status": "success",
            "message": "Firebase connectivity verified"
        }
        
        if set_data(test_path, test_data):
            print("✅ Firebase write operation successful")
        else:
            print("❌ Firebase write operation failed")
            return False
        
        # Test read operation
        read_result = get_data(test_path)
        if read_result and read_result.get("test_status") == "success":
            print("✅ Firebase read operation successful")
        else:
            print("❌ Firebase read operation failed")
            return False
        
        print("\n🎉 All Firebase tests PASSED!")
        print("✅ Firebase is ready for bot operations")
        return True
        
    except Exception as e:
        print(f"\n❌ Firebase test failed with error: {e}")
        logger.error(f"Firebase test error: {e}", exc_info=True)
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🚀 Firebase is ready! You can now start the bot system.")
        sys.exit(0)
    else:
        print("\n🛑 Firebase connectivity issues detected. Please fix before starting bots.")
        sys.exit(1)
