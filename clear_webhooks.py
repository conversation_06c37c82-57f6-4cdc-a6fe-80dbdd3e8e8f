#!/usr/bin/env python
"""
Script to clear webhooks for all bots to resolve polling conflicts.
"""

import os
import sys
import telebot

# Get bot tokens from environment variables
BOT_TOKEN = os.getenv("BOT_TOKEN")
ADMIN_BOT_TOKEN = os.getenv("ADMIN_BOT_TOKEN")
FINANCE_BOT_TOKEN = os.getenv("FINANCE_BOT_TOKEN")
MAINTENANCE_BOT_TOKEN = os.getenv("MAINTENANCE_BOT_TOKEN")
NOTIFICATION_BOT_TOKEN = os.getenv("NOTIFICATION_BOT_TOKEN")

def clear_webhook(token, bot_name):
    """Clear webhook for a specific bot"""
    try:
        bot = telebot.TeleBot(token)
        result = bot.remove_webhook()
        print(f"✅ {bot_name}: Webhook cleared successfully - {result}")
        return True
    except Exception as e:
        print(f"❌ {bot_name}: Error clearing webhook - {e}")
        return False

def main():
    """Clear webhooks for all bots"""
    print("🔧 Clearing webhooks for all bots...")
    
    bots = [
        (BOT_TOKEN, "User Bot"),
        (ADMIN_BOT_TOKEN, "Admin Bot"),
        (FINANCE_BOT_TOKEN, "Finance Bot"),
        (MAINTENANCE_BOT_TOKEN, "Maintenance Bot"),
        (NOTIFICATION_BOT_TOKEN, "Notification Bot"),
    ]
    
    success_count = 0
    for token, name in bots:
        if token:
            if clear_webhook(token, name):
                success_count += 1
        else:
            print(f"⚠️ {name}: Token not found")
    
    print(f"\n📊 Summary: {success_count}/{len(bots)} webhooks cleared successfully")
    
    if success_count == len(bots):
        print("✅ All webhooks cleared! You can now run the bots with polling.")
    else:
        print("⚠️ Some webhooks could not be cleared. Check the errors above.")

if __name__ == "__main__":
    main()
