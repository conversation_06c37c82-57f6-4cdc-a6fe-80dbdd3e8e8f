#!/usr/bin/env python3
"""
Fix the critical authorization issue for user ID 5546595738
This script will:
1. Add the user to Firebase authorized_delivery_personnel collection
2. Clear the delivery bot authorization cache
3. Test the authorization system
4. Verify the complete workflow
"""

import sys
import os
from datetime import datetime

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def add_user_to_firebase_auth():
    """Add user ID 5546595738 to Firebase authorized personnel"""
    print("🔧 Adding user 5546595738 to Firebase authorized personnel...")
    
    try:
        from src.firebase_db import get_data, set_data
        from src.bots.management_bot import safe_get_current_timestamp
        
        # Get current authorized personnel
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        
        # User details
        user_id = 5546595738
        personnel_id = f"delivery_personnel_{user_id}"
        
        # Check if user already exists
        if personnel_id in authorized_personnel:
            existing = authorized_personnel[personnel_id]
            print(f"✅ User {user_id} already exists in Firebase:")
            print(f"   Name: {existing.get('name', 'Unknown')}")
            print(f"   Status: {existing.get('status', 'unknown')}")
            print(f"   Added: {existing.get('added_date', 'unknown')}")
            
            # Update status to active if needed
            if existing.get('status') != 'active':
                existing['status'] = 'active'
                existing['reactivated_date'] = safe_get_current_timestamp()
                authorized_personnel[personnel_id] = existing
                set_data("authorized_delivery_personnel", authorized_personnel)
                print(f"✅ Updated status to active for user {user_id}")
            
            return True
        else:
            # Create new authorization record
            auth_record = {
                'telegram_id': user_id,
                'name': 'Delivery Personnel (Legacy)',
                'status': 'active',
                'added_date': safe_get_current_timestamp(),
                'added_by': 7729984017,  # Admin ID
                'source': 'manual_fix',
                'note': 'Added via authorization fix script'
            }
            
            authorized_personnel[personnel_id] = auth_record
            set_data("authorized_delivery_personnel", authorized_personnel)
            
            print(f"✅ Successfully added user {user_id} to Firebase authorized personnel")
            print(f"   Personnel ID: {personnel_id}")
            print(f"   Status: active")
            print(f"   Added: {auth_record['added_date']}")
            
            return True
            
    except Exception as e:
        print(f"❌ Error adding user to Firebase: {e}")
        import traceback
        traceback.print_exc()
        return False

def clear_delivery_bot_cache():
    """Clear the delivery bot authorization cache"""
    print("\n🔄 Clearing delivery bot authorization cache...")
    
    try:
        from src.bots.delivery_bot import clear_authorization_cache
        clear_authorization_cache()
        print("✅ Delivery bot authorization cache cleared successfully")
        return True
    except Exception as e:
        print(f"❌ Error clearing cache: {e}")
        return False

def test_authorization_system():
    """Test the authorization system for user 5546595738"""
    print("\n🧪 Testing authorization system...")
    
    try:
        from src.bots.delivery_bot import (
            is_authorized,
            get_authorized_delivery_ids_from_firebase
        )
        
        user_id = 5546595738
        
        # Test getting authorized IDs from Firebase
        print("📊 Getting authorized IDs from Firebase...")
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        print(f"✅ Retrieved {len(authorized_ids)} authorized IDs: {authorized_ids}")
        
        # Test authorization check
        print(f"\n🔐 Testing authorization for user {user_id}...")
        is_auth = is_authorized(user_id)
        
        if is_auth:
            print(f"✅ User {user_id} is successfully authorized!")
            return True
        else:
            print(f"❌ User {user_id} is still not authorized")
            return False
            
    except Exception as e:
        print(f"❌ Error testing authorization: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_firebase_data():
    """Verify the Firebase data structure"""
    print("\n🔍 Verifying Firebase data structure...")
    
    try:
        from src.firebase_db import get_data
        
        # Check authorized_delivery_personnel collection
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        print(f"📊 Firebase authorized_delivery_personnel collection: {len(authorized_personnel)} records")
        
        for personnel_id, person_data in authorized_personnel.items():
            telegram_id = person_data.get('telegram_id')
            name = person_data.get('name', 'Unknown')
            status = person_data.get('status', 'unknown')
            added_date = person_data.get('added_date', 'unknown')
            
            print(f"   👤 {personnel_id}:")
            print(f"      Name: {name}")
            print(f"      Telegram ID: {telegram_id}")
            print(f"      Status: {status}")
            print(f"      Added: {added_date}")
            
            if telegram_id == 5546595738:
                print(f"      🎯 TARGET USER FOUND!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error verifying Firebase data: {e}")
        return False

def test_delivery_bot_startup():
    """Test delivery bot authorization on startup"""
    print("\n🚀 Testing delivery bot authorization system...")
    
    try:
        # Import delivery bot functions
        from src.bots.delivery_bot import (
            get_authorized_delivery_ids_from_firebase,
            is_authorized
        )
        
        print("✅ Delivery bot functions imported successfully")
        
        # Test cache refresh
        print("🔄 Testing cache refresh...")
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        print(f"✅ Cache refreshed with {len(authorized_ids)} IDs")
        
        # Test specific user
        user_id = 5546595738
        auth_result = is_authorized(user_id)
        print(f"🔐 User {user_id} authorization: {auth_result}")
        
        return auth_result
        
    except Exception as e:
        print(f"❌ Error testing delivery bot: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_authorization_fix():
    """Run the complete authorization fix process"""
    print("🔧 CRITICAL AUTHORIZATION FIX")
    print("Fixing access for user ID: 5546595738")
    print("=" * 60)
    
    steps = [
        ("Add user to Firebase authorized personnel", add_user_to_firebase_auth),
        ("Clear delivery bot authorization cache", clear_delivery_bot_cache),
        ("Verify Firebase data structure", verify_firebase_data),
        ("Test authorization system", test_authorization_system),
        ("Test delivery bot startup", test_delivery_bot_startup)
    ]
    
    passed = 0
    failed = 0
    
    for step_name, step_func in steps:
        print(f"\n📋 Step: {step_name}")
        try:
            if step_func():
                passed += 1
                print(f"✅ {step_name} - SUCCESS")
            else:
                failed += 1
                print(f"❌ {step_name} - FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {step_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 AUTHORIZATION FIX RESULTS")
    print(f"✅ Successful steps: {passed}")
    print(f"❌ Failed steps: {failed}")
    print(f"📈 Success rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print(f"\n🎉 AUTHORIZATION FIX COMPLETE!")
        print(f"\n📋 WHAT WAS FIXED:")
        print(f"• ✅ User 5546595738 added to Firebase authorized_delivery_personnel")
        print(f"• ✅ Delivery bot authorization cache cleared and refreshed")
        print(f"• ✅ Authorization system tested and verified")
        print(f"• ✅ Enhanced debugging added to delivery bot")
        print(f"\n🚀 EXPECTED RESULTS:")
        print(f"• User 5546595738 should now have full delivery bot access")
        print(f"• No more 'Unauthorized access attempt' warnings")
        print(f"• Accept/Decline buttons should work correctly")
        print(f"• Order broadcasts and interactions should work seamlessly")
        print(f"\n📝 VERIFICATION STEPS:")
        print(f"1. Start delivery bot: python main.py --bot delivery")
        print(f"2. Have user 5546595738 send /start to delivery bot")
        print(f"3. Verify they see the delivery dashboard without errors")
        print(f"4. Test order acceptance/decline functionality")
        print(f"5. Monitor logs for detailed authorization information")
        
    else:
        print(f"\n⚠️ {failed} step(s) failed. Please review the errors above.")
        print(f"\n📝 MANUAL STEPS IF NEEDED:")
        print(f"1. Check Firebase connection and permissions")
        print(f"2. Verify user 5546595738 exists in delivery_personnel collection")
        print(f"3. Manually add to authorized_delivery_personnel if needed")
        print(f"4. Restart delivery bot to refresh authorization")
    
    return failed == 0

if __name__ == "__main__":
    success = run_authorization_fix()
    if success:
        print("\n✅ AUTHORIZATION ISSUE RESOLVED!")
        print("User 5546595738 should now have full delivery bot access.")
    else:
        print("\n❌ Authorization fix needs manual intervention.")
    sys.exit(0 if success else 1)
