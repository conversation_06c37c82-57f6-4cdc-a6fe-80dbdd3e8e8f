#!/usr/bin/env python3
"""
Script to register delivery personnel for the Wiz-Aroma system.
This script registers user 7729984017 as delivery personnel.
"""

import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.delivery_personnel_utils import create_delivery_personnel, verify_delivery_personnel
from src.config import logger

def register_user_7729984017():
    """Register user 7729984017 as delivery personnel"""
    try:
        # Get available areas (use valid area IDs from Firebase)
        service_areas = ["1", "2", "3", "4", "5"]  # All available areas
        
        # Create delivery personnel record
        personnel_id = create_delivery_personnel(
            name="Admin User",
            phone_number="+251963630623",  # Default phone number
            service_areas=service_areas,
            telegram_id="7729984017",
            email="<EMAIL>",
            vehicle_type="motorcycle",
            max_capacity=5
        )
        
        logger.info(f"Created delivery personnel with ID: {personnel_id}")
        
        # Verify the personnel immediately
        if verify_delivery_personnel(personnel_id, verified=True):
            logger.info(f"Successfully verified delivery personnel {personnel_id}")
            print(f"✅ Successfully registered and verified user 7729984017 as delivery personnel")
            print(f"Personnel ID: {personnel_id}")
            return True
        else:
            logger.error(f"Failed to verify delivery personnel {personnel_id}")
            print(f"❌ Failed to verify delivery personnel {personnel_id}")
            return False
            
    except Exception as e:
        logger.error(f"Error registering delivery personnel: {e}")
        print(f"❌ Error registering delivery personnel: {e}")
        return False

if __name__ == "__main__":
    print("🚚 Registering delivery personnel...")
    success = register_user_7729984017()
    if success:
        print("✅ Registration completed successfully!")
    else:
        print("❌ Registration failed!")
        sys.exit(1)
