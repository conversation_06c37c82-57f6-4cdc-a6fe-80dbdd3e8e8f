#!/usr/bin/env python3
"""
Test the Order Not Received fix
"""

import sys
sys.path.insert(0, 'src')

def test_import_fix():
    """Test if the import issue is fixed"""
    print("🔍 Testing import fix for Order Not Received handler...")
    
    try:
        # Test importing the function that was causing the error
        from src.data_storage import get_restaurant_by_id
        print("✅ Successfully imported get_restaurant_by_id from src.data_storage")
        
        # Test the function with a sample restaurant ID
        restaurant = get_restaurant_by_id(1)
        if restaurant:
            print(f"✅ Function works! Found restaurant: {restaurant.get('name', 'Unknown')}")
        else:
            print("⚠️ Function works but no restaurant found with ID 1")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Function error: {e}")
        return False

def test_callback_handler_import():
    """Test if the callback handler can be imported without errors"""
    print("\n🔍 Testing callback handler import...")
    
    try:
        from src.handlers.order_handlers import handle_order_not_received
        print("✅ Successfully imported handle_order_not_received")
        
        # Check if it's callable
        if callable(handle_order_not_received):
            print("✅ Function is callable")
        else:
            print("❌ Function is not callable")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_delivery_personnel_function():
    """Test delivery personnel function"""
    print("\n🔍 Testing delivery personnel function...")
    
    try:
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
        print("✅ Successfully imported get_delivery_personnel_by_id")
        
        # Test with a known personnel ID from the logs
        personnel = get_delivery_personnel_by_id("dp_19a497f8")
        if personnel:
            print(f"✅ Found personnel: {personnel.get('name', 'Unknown')}")
        else:
            print("⚠️ No personnel found with ID dp_19a497f8")
            
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_firebase_confirmed_orders():
    """Test Firebase confirmed orders access"""
    print("\n🔍 Testing Firebase confirmed orders access...")
    
    try:
        from src.firebase_db import get_data
        
        confirmed_orders = get_data("confirmed_orders")
        if confirmed_orders:
            print(f"✅ Found {len(confirmed_orders)} confirmed orders")
            
            # Find a completed order to test with
            completed_orders = []
            for order_id, order_data in confirmed_orders.items():
                if order_data.get('delivery_status') == 'completed':
                    completed_orders.append((order_id, order_data))
            
            if completed_orders:
                print(f"✅ Found {len(completed_orders)} completed orders for testing")
                sample_order_id, sample_order = completed_orders[0]
                print(f"📄 Sample order: {sample_order_id}")
                print(f"   - Restaurant: {sample_order.get('restaurant', 'N/A')}")
                print(f"   - Assigned to: {sample_order.get('assigned_to', 'N/A')}")
                print(f"   - Status: {sample_order.get('delivery_status', 'N/A')}")
            else:
                print("⚠️ No completed orders found for testing")
                
        else:
            print("❌ No confirmed orders found")
            
        return True
        
    except Exception as e:
        print(f"❌ Error accessing Firebase: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Testing Order Not Received Fix...\n")
    
    tests = [
        ("Import Fix", test_import_fix),
        ("Callback Handler Import", test_callback_handler_import),
        ("Delivery Personnel Function", test_delivery_personnel_function),
        ("Firebase Confirmed Orders", test_firebase_confirmed_orders),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("📊 ORDER NOT RECEIVED FIX TEST RESULTS")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🎉 ALL TESTS PASSED! The Order Not Received fix should work now.")
        print("💡 Next step: Test with actual order by clicking 'Order Not Received' button")
    else:
        print(f"\n⚠️ {len(results) - passed} tests failed. Please check the issues above.")

if __name__ == "__main__":
    main()
