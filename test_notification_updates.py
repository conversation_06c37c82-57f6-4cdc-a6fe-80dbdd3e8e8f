#!/usr/bin/env python3
"""
Test the updated notification formats for delivery bot and order tracking bot
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_delivery_bot_format():
    """Test the updated delivery bot message format"""
    print("📱 TESTING DELIVERY BOT FORMAT (Privacy-Protected)")
    print("=" * 55)
    
    try:
        from src.handlers.payment_handlers import format_delivery_bot_message
        
        # Create test order data
        test_order_data = {
            'restaurant_id': '1',  # This should map to "Barech" in "Bole Area"
            'phone_number': '+251912345678',
            'delivery_name': '<PERSON>',
            'delivery_location': None,
            'delivery_gate': 'Applied Library',
            'area_name': 'Campus',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-01-15 14:30:00'
        }
        
        items_text = "📋 **Order Items:**\n• Pizza Margherita x1 - 120 Birr\n• Coca Cola x1 - 30 Birr\n\n"
        
        delivery_message = format_delivery_bot_message(
            "TEST_001",
            "Barech",
            test_order_data,
            items_text,
            "2024-01-15 14:35:00"
        )
        
        print("📱 DELIVERY BOT MESSAGE:")
        print("-" * 30)
        print(delivery_message)
        
        # Verify changes
        print("\n🔍 VERIFICATION:")
        print("-" * 15)
        
        if "Restaurant Phone" not in delivery_message:
            print("✅ Restaurant phone number removed")
        else:
            print("❌ Restaurant phone number still present")
            
        if "Restaurant Area" in delivery_message:
            print("✅ Restaurant area information added")
        else:
            print("❌ Restaurant area information missing")
            
        if "Applied Library" in delivery_message:
            print("✅ Delivery location properly displayed")
        else:
            print("❌ Delivery location not displayed correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing delivery bot format: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_tracking_bot_format():
    """Test the updated order tracking bot message format"""
    print("\n🖥️ TESTING ORDER TRACKING BOT FORMAT (Administrative)")
    print("=" * 60)
    
    try:
        from src.handlers.payment_handlers import format_order_tracking_bot_message
        
        # Create test order data
        test_order_data = {
            'restaurant_id': '1',  # This should map to "Barech" in "Bole Area"
            'phone_number': '+251912345678',
            'delivery_name': 'John Doe',
            'delivery_location': None,
            'delivery_gate': 'Applied Library',
            'area_name': 'Campus',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-01-15 14:30:00'
        }
        
        items_text = "📋 **Order Items:**\n• Pizza Margherita x1 - 120 Birr\n• Coca Cola x1 - 30 Birr\n\n"
        
        tracking_message = format_order_tracking_bot_message(
            "TEST_001",
            "Barech",
            test_order_data,
            items_text,
            "2024-01-15 14:35:00"
        )
        
        print("🖥️ ORDER TRACKING BOT MESSAGE:")
        print("-" * 35)
        print(tracking_message)
        
        # Verify changes
        print("\n🔍 VERIFICATION:")
        print("-" * 15)
        
        if "Pickup Location" not in tracking_message:
            print("✅ Pickup Location field removed")
        else:
            print("❌ Pickup Location field still present")
            
        if "Restaurant Area" in tracking_message:
            print("✅ Restaurant Area field added")
        else:
            print("❌ Restaurant Area field missing")
            
        if "+251912345678" in tracking_message:
            print("✅ Customer phone number shown (administrative)")
        else:
            print("❌ Customer phone number missing")
            
        if "John Doe" in tracking_message:
            print("✅ Customer name shown (administrative)")
        else:
            print("❌ Customer name missing")
            
        if "Delivery Fee" in tracking_message:
            print("✅ Delivery fee shown (administrative)")
        else:
            print("❌ Delivery fee missing")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing order tracking bot format: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_area_retrieval():
    """Test restaurant area retrieval functionality"""
    print("\n🏢 TESTING RESTAURANT AREA RETRIEVAL")
    print("=" * 40)
    
    try:
        from src.handlers.payment_handlers import get_restaurant_area_name
        from src.data_storage import get_restaurant_by_id, get_area_by_id
        
        # Test with known restaurant ID
        test_restaurant_id = '1'  # Should be "Barech" in "Bole Area"
        
        print(f"🔍 Testing with restaurant ID: {test_restaurant_id}")
        
        # Get restaurant data
        restaurant = get_restaurant_by_id(test_restaurant_id)
        if restaurant:
            print(f"✅ Restaurant found: {restaurant['name']}")
            print(f"   Area ID: {restaurant.get('area_id')}")
            
            # Get area data
            if restaurant.get('area_id'):
                area = get_area_by_id(restaurant['area_id'])
                if area:
                    print(f"✅ Area found: {area['name']}")
                else:
                    print(f"❌ Area not found for ID: {restaurant['area_id']}")
            else:
                print("❌ No area_id in restaurant data")
        else:
            print(f"❌ Restaurant not found for ID: {test_restaurant_id}")
        
        # Test the helper function
        area_name = get_restaurant_area_name(test_restaurant_id)
        print(f"📍 get_restaurant_area_name result: '{area_name}'")
        
        if area_name and area_name != "Unknown Area":
            print("✅ Area retrieval function working")
        else:
            print("❌ Area retrieval function not working properly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing area retrieval: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_order_format():
    """Test the complete order format for tracking bot"""
    print("\n📋 TESTING COMPLETE ORDER FORMAT")
    print("=" * 35)
    
    try:
        from src.bots.order_track_bot import format_complete_order_details
        
        # Create test order data
        test_order_data = {
            'restaurant_id': '1',
            'phone_number': '+251912345678',
            'delivery_name': 'John Doe',
            'delivery_location': None,
            'delivery_gate': 'Applied Library',
            'subtotal': 150,
            'delivery_fee': 25,
            'created_at': '2024-01-15 14:30:00',
            'approved_at': '2024-01-15 14:35:00',
            'items': [
                {'name': 'Pizza Margherita', 'price': 120, 'quantity': 1},
                {'name': 'Coca Cola', 'price': 30, 'quantity': 1}
            ]
        }
        
        # Test different status scenarios
        test_cases = [
            {
                'title': 'NEW ORDER AVAILABLE',
                'status': 'Payment Approved - Broadcasting to delivery personnel',
                'timing': ''
            },
            {
                'title': 'ORDER ASSIGNED TO DELIVERY PERSONNEL',
                'status': 'Assigned to John Smith (0912345678)',
                'timing': '• Assigned to Delivery: 2024-01-15 14:40:00\n'
            },
            {
                'title': 'DELIVERY COMPLETED',
                'status': 'Driver John Smith has marked the order as delivered. Customer confirmation request sent.',
                'timing': '• Completed by Driver: 2024-01-15 15:00:00\n'
            },
            {
                'title': 'ORDER FULLY COMPLETED',
                'status': 'Customer has confirmed receipt of the order. Order process complete.',
                'timing': '• Customer Confirmed: 2024-01-15 15:05:00\n'
            }
        ]
        
        for i, test_case in enumerate(test_cases, 1):
            print(f"\n📋 TEST CASE {i}: {test_case['title']}")
            print("-" * 50)
            
            message = format_complete_order_details(
                "TEST_001",
                test_order_data,
                test_case['title'],
                test_case['status'],
                test_case['timing']
            )
            
            print(message)
            
            # Verify structure
            required_elements = [
                "Order #TEST_001",
                "Restaurant Area",
                "Customer Details",
                "Order Items",
                "Order Summary",
                "Timing",
                "Status"
            ]
            
            missing_elements = []
            for element in required_elements:
                if element not in message:
                    missing_elements.append(element)
            
            if not missing_elements:
                print("✅ All required elements present")
            else:
                print(f"❌ Missing elements: {', '.join(missing_elements)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing complete order format: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🔧 NOTIFICATION FORMAT UPDATE VERIFICATION")
    print("=" * 60)
    print("Testing updated delivery bot and order tracking bot formats")
    print()
    
    # Run all tests
    tests = [
        test_delivery_bot_format,
        test_order_tracking_bot_format,
        test_area_retrieval,
        test_complete_order_format
    ]
    
    results = []
    for test in tests:
        try:
            result = test()
            results.append(result)
        except Exception as e:
            print(f"❌ Test failed: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    successful_tests = sum(results)
    total_tests = len(results)
    
    print(f"📊 TEST SUMMARY: {successful_tests}/{total_tests} tests passed")
    
    if all(results):
        print("✅ ALL TESTS PASSED - Notification format updates working!")
        print("\n📋 CHANGES VERIFIED:")
        print("• Delivery bot: Restaurant phone removed, area added")
        print("• Order tracking bot: Pickup location replaced with area")
        print("• Complete order format maintains consistent structure")
        print("• Restaurant area retrieval working correctly")
        print("• Privacy controls maintained")
    else:
        print("❌ SOME TESTS FAILED - Review the output above")
    
    return all(results)

if __name__ == "__main__":
    main()
