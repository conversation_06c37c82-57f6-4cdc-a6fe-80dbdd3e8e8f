#!/usr/bin/env python3
"""
Test script to verify the fixes for order flow integration and delivery personnel registration.
"""

import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.firebase_db import get_data, set_data
from src.utils.delivery_personnel_utils import get_delivery_personnel_by_telegram_id
from src.config import logger

def test_delivery_personnel_registration():
    """Test if user 7729984017 is properly registered as delivery personnel"""
    print("🧪 Testing delivery personnel registration...")
    
    try:
        personnel = get_delivery_personnel_by_telegram_id("7729984017")
        if personnel:
            print(f"✅ User 7729984017 is registered as delivery personnel")
            print(f"   Personnel ID: {personnel.personnel_id}")
            print(f"   Name: {personnel.name}")
            print(f"   Phone: {personnel.phone_number}")
            print(f"   Service Areas: {personnel.service_areas}")
            print(f"   Verified: {personnel.is_verified}")
            return True
        else:
            print("❌ User 7729984017 is NOT registered as delivery personnel")
            return False
    except Exception as e:
        print(f"❌ Error checking delivery personnel registration: {e}")
        return False

def test_confirmed_orders_collection():
    """Test if confirmed_orders collection exists and can be accessed"""
    print("\n🧪 Testing confirmed orders collection...")
    
    try:
        # Try to read confirmed orders
        confirmed_orders = get_data("confirmed_orders")
        print(f"✅ Confirmed orders collection accessible")
        
        if confirmed_orders:
            print(f"   Found {len(confirmed_orders)} confirmed orders")
            for order_number, order_data in confirmed_orders.items():
                print(f"   Order #{order_number}: Status = {order_data.get('status', 'Unknown')}")
        else:
            print("   No confirmed orders found (collection is empty)")
        
        # Test writing to confirmed orders collection
        test_order = {
            "order_number": "TEST_001",
            "user_id": "test_user",
            "status": "CONFIRMED",
            "delivery_status": "pending_assignment",
            "confirmed_at": "2025-06-30 14:30:00",
            "restaurant_id": "1",
            "subtotal": 100,
            "phone_number": "+251963630623",
            "delivery_location": "Test Location"
        }
        
        if set_data("confirmed_orders/TEST_001", test_order):
            print("✅ Successfully wrote test order to confirmed_orders collection")
            
            # Clean up test order
            from src.firebase_db import delete_data
            delete_data("confirmed_orders/TEST_001")
            print("✅ Successfully cleaned up test order")
            return True
        else:
            print("❌ Failed to write to confirmed_orders collection")
            return False
            
    except Exception as e:
        print(f"❌ Error testing confirmed orders collection: {e}")
        return False

def test_bot_authorization():
    """Test bot authorization for user 7729984017"""
    print("\n🧪 Testing bot authorization...")
    
    try:
        from src.config import DELIVERY_BOT_AUTHORIZED_IDS, ORDER_TRACK_BOT_AUTHORIZED_IDS
        
        # Test delivery bot authorization
        if 7729984017 in DELIVERY_BOT_AUTHORIZED_IDS:
            print("✅ User 7729984017 is authorized for delivery bot")
        else:
            print("❌ User 7729984017 is NOT authorized for delivery bot")
            
        # Test order tracking bot authorization
        if 7729984017 in ORDER_TRACK_BOT_AUTHORIZED_IDS:
            print("✅ User 7729984017 is authorized for order tracking bot")
        else:
            print("❌ User 7729984017 is NOT authorized for order tracking bot")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing bot authorization: {e}")
        return False

def main():
    """Run all tests"""
    print("🔧 Testing Wiz-Aroma Order Flow Integration and Delivery Personnel Registration Fixes")
    print("=" * 80)
    
    test_results = []
    
    # Test 1: Delivery Personnel Registration
    test_results.append(test_delivery_personnel_registration())
    
    # Test 2: Confirmed Orders Collection
    test_results.append(test_confirmed_orders_collection())
    
    # Test 3: Bot Authorization
    test_results.append(test_bot_authorization())
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST SUMMARY")
    print("=" * 80)
    
    passed = sum(test_results)
    total = len(test_results)
    
    if passed == total:
        print(f"✅ ALL TESTS PASSED ({passed}/{total})")
        print("\n🎉 Both critical issues have been successfully fixed!")
        print("   • Order flow integration: Orders are now stored in confirmed_orders collection")
        print("   • Delivery personnel registration: User 7729984017 is registered and verified")
        return True
    else:
        print(f"❌ SOME TESTS FAILED ({passed}/{total})")
        print("\n⚠️  Some issues may still need attention.")
        return False

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
