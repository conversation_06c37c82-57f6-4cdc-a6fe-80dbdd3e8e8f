#!/usr/bin/env python3
"""
Management Bot Polling Test
Tests the actual polling functionality of the management bot
"""

import sys
import os
import time
import signal

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_management_bot_polling():
    """Test management bot with actual polling"""
    print("🧪 MANAGEMENT BOT POLLING TEST")
    print("=" * 50)
    
    try:
        # Import required modules
        from src.bots.management_bot import management_bot, register_management_bot_handlers
        from src.config import logger
        
        print("✅ Management bot imported")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        print(f"✅ Bot ID: {bot_info.id}")
        
        # Clear any existing handlers
        management_bot.message_handlers.clear()
        management_bot.callback_query_handlers.clear()
        print("✅ Cleared existing handlers")
        
        # Register handlers
        register_management_bot_handlers()
        print(f"✅ Registered {len(management_bot.message_handlers)} message handlers")
        print(f"✅ Registered {len(management_bot.callback_query_handlers)} callback handlers")
        
        # Add a custom message handler for testing
        @management_bot.message_handler(commands=['ping'])
        def ping_handler(message):
            management_bot.reply_to(message, "🏓 Pong! Management bot is responding!")
            print(f"📨 Received ping from user {message.from_user.id}")
        
        print("✅ Added ping test handler")
        
        # Remove webhook to ensure polling works
        try:
            management_bot.remove_webhook()
            print("✅ Webhook removed")
        except Exception as e:
            print(f"⚠️ Webhook removal: {e}")
        
        print("\n🚀 Starting Management Bot...")
        print("=" * 50)
        print("Bot is now listening for messages!")
        print("Send the following commands to @Wiz_Aroma_Finance_bot:")
        print("  /start - Show management menu")
        print("  /ping  - Test response")
        print("  /help  - Show help")
        print("\nPress Ctrl+C to stop the bot...")
        print("=" * 50)
        
        # Set up signal handler for graceful shutdown
        def signal_handler(sig, frame):
            print("\n⏹️ Stopping bot...")
            management_bot.stop_polling()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        # Start polling with detailed logging
        try:
            management_bot.infinity_polling(
                timeout=60,
                long_polling_timeout=60,
                none_stop=True,
                interval=1
            )
        except KeyboardInterrupt:
            print("\n⏹️ Bot stopped by user")
        except Exception as e:
            print(f"\n❌ Polling error: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_management_bot_polling()
