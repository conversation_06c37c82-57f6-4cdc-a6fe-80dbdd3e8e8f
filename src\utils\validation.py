"""
Validation utility functions for the Wiz Aroma Delivery Bot.
"""

import re


def is_valid_phone(phone):
    """Validate phone number format"""
    patterns = [
        r"^09\d{8}$",  # 09xxxxxxxx
        r"^07\d{8}$",  # 07xxxxxxxx
        r"^\+2519\d{8}$",  # +2519xxxxxxxx
        r"^\+2517\d{8}$",  # +2517xxxxxxxx
    ]
    return any(re.match(pattern, phone) for pattern in patterns)


def calculate_points(delivery_fee):
    """Calculate points as 11% of delivery fee, minimum 1 point
    The decimal part is truncated (not rounded) as per business rules
    Displayed to users as (10 + 1)% for marketing appeal"""
    points = max(1, int(delivery_fee * 0.11))
    return points
