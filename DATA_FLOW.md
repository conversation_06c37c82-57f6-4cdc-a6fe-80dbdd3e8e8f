# Data Flow

This document provides a detailed explanation of the data flow in the Wiz Aroma Delivery Bot system, from order placement to delivery.

## Order Flow

The order flow is the main process in the system, involving all four bots and multiple data structures.

### 1. Order Placement (User Bot)

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Select     │────►│  Select     │────►│  Add Menu   │────►│  Add Order  │
│   Area      │     │ Restaurant  │     │   Items     │     │ Description │
│             │     │             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                                                                   │
                                                                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │     │             │
│  Confirm    │◄────│  Review     │◄────│  Provide    │◄────│  Select     │
│   Order     │     │   Order     │     │ Phone Number│     │ Delivery    │
│             │     │             │     │             │     │  Location   │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
       │
       ▼
┌─────────────┐
│             │
│  Submit for │
│Admin Review │
│             │
└─────────────┘
```

**Data Structures Used:**
- `orders`: Stores the current order being created
- `order_status`: Tracks the status of the order
- `user_names`: Stores user names for future orders
- `user_phone_numbers`: Stores user phone numbers for future orders
- `current_order_numbers`: Stores the order number for the current order

**Data Flow:**
1. User selects an area → Area ID stored in `orders[user_id]`
2. User selects a restaurant → Restaurant ID stored in `orders[user_id]`
3. User adds menu items → Items stored in `orders[user_id]["items"]`
4. User adds order description (optional) → Description stored in `orders[user_id]["special_instructions"]`
5. User selects delivery location → Location stored in `orders[user_id]["delivery_location"]`
6. User provides delivery name → Name stored in `orders[user_id]["delivery_name"]` and `user_names[user_id]`
7. User provides phone number → Phone number stored in `orders[user_id]["phone_number"]` and `user_phone_numbers[user_id]`
8. User reviews order → Order summary displayed
9. User confirms order → Order submitted for admin review

### 2. Admin Review (Admin Bot)

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Receive    │────►│  Review     │────►│ Add Remarks │
│   Order     │     │   Order     │     │ (Optional)  │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
                                               │
                                               ▼
                    ┌─────────────┐     ┌─────────────┐
                    │             │     │             │
                    │  Reject     │◄────┤  Approve or │
                    │   Order     │     │   Reject    │
                    │             │     │             │
                    └─────────────┘     └─────────────┘
                           │                   │
                           ▼                   ▼
                    ┌─────────────┐     ┌─────────────┐
                    │             │     │             │
                    │  Notify     │     │  Notify     │
                    │   User      │     │   User      │
                    │             │     │             │
                    └─────────────┘     └─────────────┘
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │             │
                                        │ Proceed to  │
                                        │  Payment    │
                                        │             │
                                        └─────────────┘
```

**Data Structures Used:**
- `pending_admin_reviews`: Stores orders waiting for admin review
- `admin_remarks`: Stores admin remarks on orders
- `order_status`: Updates the status of the order

**Data Flow:**
1. Admin receives order notification → Order details displayed from `pending_admin_reviews[order_number]`
2. Admin reviews order → No data change
3. Admin adds remarks (optional) → Remarks stored in `admin_remarks[order_number]`
4. Admin approves or rejects order:
   - If approved → Order status updated to "AWAITING_PAYMENT_METHOD" in `order_status[user_id]`
   - If rejected → Order status updated to "REJECTED" in `order_status[user_id]`
5. User notified of admin decision → No data change

### 3. Payment Processing (User Bot & Finance Bot)

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Select     │────►│  Use Points │────►│  Confirm    │
│  Payment    │     │ (Optional)  │     │  Points Use │
│  Method     │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
       │                                       │
       │                                       ▼
       │                               ┌─────────────┐
       │                               │             │
       │                               │  Update     │
       │                               │  Points     │
       │                               │             │
       │                               └─────────────┘
       │                                       │
       ▼                                       │
┌─────────────┐     ┌─────────────┐           │
│             │     │             │           │
│  Send       │────►│  Receive    │           │
│  Payment    │     │  Receipt    │           │
│  Receipt    │     │ (Finance)   │           │
└─────────────┘     └─────────────┘           │
                           │                   │
                           ▼                   │
                    ┌─────────────┐           │
                    │             │           │
                    │  Verify     │           │
                    │  Payment    │           │
                    │             │           │
                    └─────────────┘           │
                           │                   │
                           ▼                   │
                    ┌─────────────┐     ┌─────────────┐
                    │             │     │             │
                    │  Notify     │────►│  Update     │
                    │   User      │     │  Order      │
                    │             │     │  Status     │
                    └─────────────┘     └─────────────┘
```

**Data Structures Used:**
- `order_status`: Tracks the status of the order
- `user_points`: Stores user points balance
- `awaiting_receipt`: Stores orders waiting for payment receipt
- `orders`: Updates the payment method and status

**Data Flow:**
1. User selects payment method → Payment method stored in `orders[user_id]["payment_method"]`
2. If using points:
   - Points balance checked in `user_points[user_id]`
   - Points deducted from `user_points[user_id]`
   - Order marked as paid in `orders[user_id]["paid"] = True`
3. If using other payment methods:
   - User sends payment receipt → Receipt forwarded to Finance Bot
   - Order added to `awaiting_receipt[order_number]`
   - Finance verifies payment → Order status updated to "PAYMENT_VERIFIED" in `order_status[user_id]`
4. User notified of payment verification → No data change

### 4. Order Fulfillment

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Order      │────►│  Order      │────►│  Order      │
│  Confirmed  │     │ In Progress │     │  Delivered  │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │             │
                                        │  Save to    │
                                        │  History    │
                                        │             │
                                        └─────────────┘
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │             │
                                        │  Award      │
                                        │  Points     │
                                        │             │
                                        └─────────────┘
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │             │
                                        │  Clean Up   │
                                        │  Order Data │
                                        │             │
                                        └─────────────┘
```

**Data Structures Used:**
- `order_status`: Tracks the status of the order
- `user_order_history`: Stores completed orders
- `user_points`: Updates user points balance
- `orders`: Cleaned up after completion

**Data Flow:**
1. Order confirmed → Order status updated to "CONFIRMED" in `order_status[user_id]`
2. Order in progress → Order status updated to "IN_PROGRESS" in `order_status[user_id]`
3. Order delivered → Order status updated to "DELIVERED" in `order_status[user_id]`
4. Order saved to history → Order added to `user_order_history[user_id]`
5. Points awarded → Points added to `user_points[user_id]`
6. Order data cleaned up → Order removed from `orders`, `order_status`, etc.

## Favorite Orders Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Save Order │────►│  Name       │────►│  Save to    │
│ as Favorite │     │  Favorite   │     │  Favorites  │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │             │
                                        │  View       │
                                        │ Favorites   │
                                        │             │
                                        └─────────────┘
                                               │
                                               ▼
                                        ┌─────────────┐
                                        │             │
                                        │  Reorder    │
                                        │ Favorite    │
                                        │             │
                                        └─────────────┘
```

**Data Structures Used:**
- `favorite_orders`: Stores user favorite orders
- `orders`: Used when reordering a favorite

**Data Flow:**
1. User saves order as favorite → Order details stored in `favorite_orders[user_id]`
2. User views favorites → Favorites displayed from `favorite_orders[user_id]`
3. User reorders favorite → Favorite order details copied to `orders[user_id]`

## Points System Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Earn       │────►│  View       │────►│  Use        │
│  Points     │     │  Points     │     │  Points     │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
```

**Data Structures Used:**
- `user_points`: Stores user points balance

**Data Flow:**
1. User earns points → Points added to `user_points[user_id]`
2. User views points → Points balance displayed from `user_points[user_id]`
3. User uses points → Points deducted from `user_points[user_id]`

## Maintenance Flow

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Manage     │────►│  Manage     │────►│  Manage     │
│   Areas     │     │ Restaurants │     │   Menus     │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
       │                   │                   │
       ▼                   ▼                   ▼
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Add/Edit/  │     │  Add/Edit/  │     │  Add/Edit/  │
│   Delete    │     │   Delete    │     │   Delete    │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
```

**Data Structures Used:**
- `areas_data`: Stores area information
- `restaurants_data`: Stores restaurant information
- `menus_data`: Stores menu information
- `delivery_locations_data`: Stores delivery location information
- `delivery_fees_data`: Stores delivery fee information

**Data Flow:**
1. Maintenance user manages areas → Area data updated in `areas_data`
2. Maintenance user manages restaurants → Restaurant data updated in `restaurants_data`
3. Maintenance user manages menus → Menu data updated in `menus_data`
4. Maintenance user manages delivery locations → Location data updated in `delivery_locations_data`
5. Maintenance user manages delivery fees → Fee data updated in `delivery_fees_data`

## Data Persistence

All data structures are periodically saved to JSON files for persistence:

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  In-Memory  │────►│  Backup     │────►│  Save to    │
│    Data     │     │  Existing   │     │  JSON File  │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
```

**Process:**
1. In-memory data is modified during operation
2. Existing data file is backed up (if it exists)
3. New data is saved to JSON file
4. Data is loaded from JSON file on startup

## Data Consistency

The system includes a data consistency manager to ensure data is consistent across all components:

```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│             │     │             │     │             │
│  Register   │────►│  Schedule   │────►│  Refresh    │
│  Data Type  │     │  Refresh    │     │   Data      │
│             │     │             │     │             │
└─────────────┘     └─────────────┘     └─────────────┘
```

**Process:**
1. Data types are registered with the consistency manager
2. Refresh intervals are scheduled for each data type
3. Data is refreshed at the scheduled intervals
4. If data is corrupted, it is restored from backup
