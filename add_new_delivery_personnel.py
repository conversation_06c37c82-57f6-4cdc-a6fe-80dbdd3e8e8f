#!/usr/bin/env python3
"""
Add new delivery personnel with Telegram ID 1133538088 to the Wiz-Aroma system.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_delivery_personnel_1133538088():
    """Add new delivery personnel with Telegram ID 1133538088"""
    print("🚚 ADDING NEW DELIVERY PERSONNEL")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import create_delivery_personnel, verify_delivery_personnel
        from src.firebase_db import get_data
        
        # Check if this Telegram ID already exists
        existing_personnel = get_data("delivery_personnel") or {}
        telegram_id = "1133538088"
        
        existing_id = None
        for pid, pdata in existing_personnel.items():
            if pdata.get('telegram_id') == telegram_id:
                existing_id = pid
                break
        
        if existing_id:
            print(f"⚠️  Personnel with Telegram ID {telegram_id} already exists: {existing_id}")
            return existing_id
        
        # Create new delivery personnel
        print(f"📝 Creating new delivery personnel with Telegram ID: {telegram_id}")
        
        personnel_id = create_delivery_personnel(
            name="New Delivery Personnel",
            phone_number="+251987654321",
            service_areas=["1", "2", "3", "4"],  # Service areas 1-4
            telegram_id=telegram_id,
            email="<EMAIL>",
            vehicle_type="motorcycle",
            max_capacity=5
        )
        
        print(f"✅ Created delivery personnel with ID: {personnel_id}")
        
        # Verify the personnel immediately
        if verify_delivery_personnel(personnel_id, verified=True):
            print(f"✅ Successfully verified delivery personnel {personnel_id}")
        else:
            print(f"⚠️  Failed to verify delivery personnel {personnel_id}")
        
        return personnel_id
        
    except Exception as e:
        print(f"❌ Error adding delivery personnel: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_delivery_personnel_integration():
    """Verify the new delivery personnel is properly integrated"""
    print("\n🔍 VERIFYING DELIVERY PERSONNEL INTEGRATION")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        # Check Firebase data
        personnel_data = get_data("delivery_personnel") or {}
        availability_data = get_data("delivery_personnel_availability") or {}
        capacity_data = get_data("delivery_personnel_capacity") or {}
        zones_data = get_data("delivery_personnel_zones") or {}
        
        telegram_id = "1133538088"
        found_personnel = None
        
        for pid, pdata in personnel_data.items():
            if pdata.get('telegram_id') == telegram_id:
                found_personnel = (pid, pdata)
                break
        
        if found_personnel:
            pid, pdata = found_personnel
            print(f"✅ Found personnel in Firebase:")
            print(f"   Personnel ID: {pid}")
            print(f"   Name: {pdata.get('name', 'Unknown')}")
            print(f"   Telegram ID: {pdata.get('telegram_id', 'Unknown')}")
            print(f"   Service Areas: {pdata.get('service_areas', [])}")
            print(f"   Max Capacity: {pdata.get('max_capacity', 0)}")
            print(f"   Status: {pdata.get('status', 'Unknown')}")
            print(f"   Verified: {pdata.get('is_verified', False)}")
            
            # Check availability data
            if pid in availability_data:
                print(f"   Availability: {availability_data[pid]}")
            else:
                print(f"   ⚠️  No availability data found")
            
            # Check capacity data
            if pid in capacity_data:
                print(f"   Current Capacity: {capacity_data[pid]}")
            else:
                print(f"   ⚠️  No capacity data found")
            
            # Check zones data
            if pid in zones_data:
                print(f"   Zone Assignment: {zones_data[pid]}")
            else:
                print(f"   ⚠️  No zones data found")
            
            # Test availability for order assignment
            print(f"\n🔍 Testing availability for order assignment...")
            available_personnel = find_available_personnel("3")  # Test with area 3
            
            available_ids = [p.personnel_id for p in available_personnel if hasattr(p, 'personnel_id')]
            if pid in available_ids:
                print(f"✅ Personnel {pid} is available for area 3 assignments")
            else:
                print(f"⚠️  Personnel {pid} is NOT available for area 3 assignments")
                print(f"   Available personnel: {available_ids}")
            
            return True
        else:
            print(f"❌ Personnel with Telegram ID {telegram_id} NOT found in Firebase")
            return False
            
    except Exception as e:
        print(f"❌ Error verifying integration: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_bot_recognition():
    """Test if delivery bot recognizes the new personnel"""
    print("\n🤖 TESTING DELIVERY BOT RECOGNITION")
    print("=" * 60)
    
    try:
        from src.bots.delivery_bot import get_personnel_by_telegram_id
        
        telegram_id = 1133538088  # As integer for bot function
        personnel = get_personnel_by_telegram_id(telegram_id)
        
        if personnel:
            print(f"✅ Delivery bot recognizes personnel:")
            print(f"   Personnel ID: {personnel.personnel_id}")
            print(f"   Name: {personnel.name}")
            print(f"   Telegram ID: {personnel.telegram_id}")
            print(f"   Service Areas: {personnel.service_areas}")
            print(f"   Max Capacity: {personnel.max_capacity}")
            print(f"   Status: {personnel.status}")
            print(f"   Verified: {personnel.is_verified}")
            return True
        else:
            print(f"❌ Delivery bot does NOT recognize Telegram ID {telegram_id}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery bot recognition: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to add and verify new delivery personnel"""
    print("🚀 ADDING NEW DELIVERY PERSONNEL TO WIZ-AROMA SYSTEM")
    print("=" * 80)
    
    # Step 1: Add the new delivery personnel
    personnel_id = add_delivery_personnel_1133538088()
    
    if not personnel_id:
        print("❌ Failed to add delivery personnel. Exiting.")
        return False
    
    # Step 2: Verify integration
    integration_ok = verify_delivery_personnel_integration()
    
    # Step 3: Test delivery bot recognition
    bot_recognition_ok = test_delivery_bot_recognition()
    
    print("\n" + "=" * 80)
    print("📊 SUMMARY:")
    print(f"   Personnel Creation: {'✅ Success' if personnel_id else '❌ Failed'}")
    print(f"   Integration Check: {'✅ Success' if integration_ok else '❌ Failed'}")
    print(f"   Bot Recognition: {'✅ Success' if bot_recognition_ok else '❌ Failed'}")
    
    if personnel_id and integration_ok and bot_recognition_ok:
        print("\n🎉 NEW DELIVERY PERSONNEL SUCCESSFULLY ADDED!")
        print(f"   Personnel ID: {personnel_id}")
        print(f"   Telegram ID: 1133538088")
        print(f"   Ready for order assignments and delivery workflow")
        return True
    else:
        print("\n⚠️  Some issues detected. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
