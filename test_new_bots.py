#!/usr/bin/env python
"""
Test script for new bots (order tracking and delivery bots)
"""

import telebot
from src.config import (
    ORDER_TRACK_BOT_TOKEN,
    DELIVERY_BOT_TOKEN,
    ORDER_TRACK_BOT_AUTHORIZED_IDS,
    DELIVERY_BOT_AUTHORIZED_IDS,
    logger
)

def test_bot_connection(token, bot_name):
    """Test if a bot token can connect to Telegram"""
    try:
        bot = telebot.TeleBot(token)
        bot_info = bot.get_me()
        logger.info(f"{bot_name} connected successfully: @{bot_info.username}")
        return True
    except Exception as e:
        logger.error(f"Failed to connect {bot_name}: {e}")
        return False

def main():
    """Test the new bot configurations"""
    logger.info("Testing new bot configurations...")
    
    # Test order tracking bot
    logger.info("Testing Order Tracking Bot...")
    logger.info(f"Token: {ORDER_TRACK_BOT_TOKEN[:10]}...")
    logger.info(f"Authorized IDs: {ORDER_TRACK_BOT_AUTHORIZED_IDS}")
    order_track_success = test_bot_connection(ORDER_TRACK_BOT_TOKEN, "Order Tracking Bot")
    
    # Test delivery bot
    logger.info("Testing Delivery Bot...")
    logger.info(f"Token: {DELIVERY_BOT_TOKEN[:10]}...")
    logger.info(f"Authorized IDs: {DELIVERY_BOT_AUTHORIZED_IDS}")
    delivery_success = test_bot_connection(DELIVERY_BOT_TOKEN, "Delivery Bot")
    
    # Summary
    logger.info("=== Test Results ===")
    logger.info(f"Order Tracking Bot: {'✅ SUCCESS' if order_track_success else '❌ FAILED'}")
    logger.info(f"Delivery Bot: {'✅ SUCCESS' if delivery_success else '❌ FAILED'}")
    
    if order_track_success and delivery_success:
        logger.info("🎉 All new bots configured successfully!")
        return True
    else:
        logger.error("❌ Some bots failed to connect. Check tokens and network connection.")
        return False

if __name__ == "__main__":
    main()
