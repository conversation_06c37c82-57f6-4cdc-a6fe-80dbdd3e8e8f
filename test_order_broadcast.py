#!/usr/bin/env python3
"""
Test order broadcast to delivery personnel
"""

import sys
import os
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_order_broadcast():
    """Test order broadcast functionality"""
    print("=== TESTING ORDER BROADCAST ===")
    
    try:
        # Import required modules
        from src.firebase_db import get_data, set_data
        from src.config import logger
        
        # 1. Check current delivery personnel data in Firebase
        print("1. Checking Firebase delivery personnel data...")
        personnel_data = get_data("delivery_personnel") or {}
        availability_data = get_data("delivery_personnel_availability") or {}
        capacity_data = get_data("delivery_personnel_capacity") or {}
        
        print(f"Personnel count: {len(personnel_data)}")
        print(f"Availability count: {len(availability_data)}")
        print(f"Capacity count: {len(capacity_data)}")
        
        target_telegram_id = "1133538088"
        target_personnel_id = None
        
        for pid, pdata in personnel_data.items():
            if pdata.get('telegram_id') == target_telegram_id:
                target_personnel_id = pid
                print(f"\n✅ Found target personnel: {pid}")
                print(f"  Name: {pdata.get('name')}")
                print(f"  Status: {pdata.get('status')}")
                print(f"  Verified: {pdata.get('is_verified')}")
                print(f"  Service Areas: {pdata.get('service_areas')}")
                break
        
        if not target_personnel_id:
            print(f"❌ Personnel with Telegram ID {target_telegram_id} not found")
            return False
        
        # Check availability and capacity
        availability = availability_data.get(target_personnel_id, "NOT_SET")
        capacity = capacity_data.get(target_personnel_id, "NOT_SET")
        
        print(f"  Availability: {availability}")
        print(f"  Capacity: {capacity}")
        
        # 2. Create a test order to trigger broadcast
        print(f"\n2. Creating test order...")
        
        # Generate test order data
        import datetime
        current_time = datetime.datetime.now()
        order_number = f"7729984017_{current_time.strftime('%d%m%y%H%M')}_TEST"
        
        test_order = {
            "order_number": order_number,
            "user_id": "7729984017",
            "restaurant_id": "rest_001",
            "restaurant_area_id": "1",  # This should match personnel service areas
            "area_id": "1",
            "items": [
                {
                    "name": "Test Coffee",
                    "price": 50.0,
                    "quantity": 1
                }
            ],
            "total_amount": 50.0,
            "delivery_fee": 10.0,
            "delivery_address": "Test Address, Area 1",
            "customer_phone": "+251912345678",
            "status": "payment_approved",
            "created_at": current_time.strftime("%Y-%m-%d %H:%M:%S"),
            "payment_method": "cash",
            "special_instructions": "Test order for delivery broadcast"
        }
        
        # Save test order to Firebase
        set_data(f"orders/{order_number}", test_order)
        print(f"✅ Created test order: {order_number}")
        
        # 3. Simulate the delivery broadcast logic
        print(f"\n3. Testing delivery broadcast logic...")
        
        try:
            # Import the broadcast function components
            from src.utils.delivery_personnel_utils import find_available_personnel
            from src.config import DELIVERY_BOT_TOKEN
            import telebot
            
            # Get delivery area ID from order
            delivery_area_id = test_order.get('restaurant_area_id') or test_order.get('area_id')
            print(f"Order delivery area: {delivery_area_id}")
            
            if not delivery_area_id:
                print(f"❌ No delivery area ID found")
                return False
            
            # Find available delivery personnel
            available_personnel_ids = find_available_personnel(str(delivery_area_id))
            print(f"Available personnel for area {delivery_area_id}: {available_personnel_ids}")
            
            if not available_personnel_ids:
                print(f"❌ No available personnel found for area {delivery_area_id}")
                
                # Debug: Check all personnel manually
                print(f"\n🔍 DEBUG: Manual personnel check...")
                for pid, pdata in personnel_data.items():
                    availability = availability_data.get(pid, 'unknown')
                    capacity = capacity_data.get(pid, 'unknown')
                    service_areas = pdata.get('service_areas', [])
                    status = pdata.get('status', 'unknown')
                    verified = pdata.get('is_verified', False)
                    
                    print(f"  {pid} ({pdata.get('name')}):")
                    print(f"    Telegram ID: {pdata.get('telegram_id')}")
                    print(f"    Status: {status}")
                    print(f"    Verified: {verified}")
                    print(f"    Availability: {availability}")
                    print(f"    Capacity: {capacity}")
                    print(f"    Service Areas: {service_areas}")
                    print(f"    Can serve area {delivery_area_id}: {delivery_area_id in service_areas}")
                    
                    # Check availability criteria
                    is_available = (
                        status == "available" and
                        verified == True and
                        availability == "available" and
                        delivery_area_id in service_areas
                    )
                    print(f"    Would be available: {is_available}")
                    print()
                
                return False
            else:
                print(f"✅ Found {len(available_personnel_ids)} available personnel")
                
                # Check if our target personnel is included
                if target_personnel_id in available_personnel_ids:
                    print(f"✅ Target personnel {target_personnel_id} ({target_telegram_id}) is in available list!")
                    
                    # Test sending a message (dry run)
                    print(f"\n4. Testing message delivery (dry run)...")
                    
                    try:
                        delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)
                        
                        # Create test message
                        test_message = f"""
🚨 **NEW ORDER AVAILABLE** 🚨

📋 **Order #{order_number}**
🏪 **Restaurant:** Test Restaurant
📍 **Delivery Address:** {test_order['delivery_address']}
📞 **Customer Phone:** {test_order['customer_phone']}

💰 **Order Total:** {test_order['total_amount']} ETB
🚚 **Delivery Fee:** {test_order['delivery_fee']} ETB

⚡ **First to accept gets the order!**
"""
                        
                        print(f"Would send message to Telegram ID {target_telegram_id}:")
                        print(test_message)
                        print(f"✅ Message preparation successful")
                        
                        return True
                        
                    except Exception as msg_error:
                        print(f"❌ Message preparation failed: {msg_error}")
                        return False
                else:
                    print(f"❌ Target personnel {target_personnel_id} ({target_telegram_id}) is NOT in available list")
                    print(f"Available personnel: {available_personnel_ids}")
                    return False
        
        except Exception as broadcast_error:
            print(f"❌ Broadcast logic test failed: {broadcast_error}")
            import traceback
            traceback.print_exc()
            return False
    
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_order():
    """Clean up test order"""
    try:
        from src.firebase_db import get_data, set_data
        
        # Find and remove test orders
        orders_data = get_data("orders") or {}
        test_orders = [order_id for order_id in orders_data.keys() if "_TEST" in order_id]
        
        for order_id in test_orders:
            set_data(f"orders/{order_id}", None)  # Delete the order
            print(f"🗑️  Cleaned up test order: {order_id}")
        
    except Exception as e:
        print(f"⚠️  Cleanup failed: {e}")

if __name__ == "__main__":
    print("🧪 TESTING ORDER BROADCAST FUNCTIONALITY")
    print("=" * 60)
    
    try:
        success = test_order_broadcast()
        
        if success:
            print(f"\n🎉 ORDER BROADCAST TEST SUCCESSFUL!")
            print(f"Personnel 1133538088 should receive order notifications!")
        else:
            print(f"\n❌ ORDER BROADCAST TEST FAILED")
            print(f"Personnel 1133538088 will NOT receive order notifications")
        
        # Clean up
        print(f"\n🧹 Cleaning up test data...")
        cleanup_test_order()
        
    except KeyboardInterrupt:
        print(f"\n⚠️  Test interrupted by user")
        cleanup_test_order()
    except Exception as e:
        print(f"\n💥 Test crashed: {e}")
        cleanup_test_order()
