#!/usr/bin/env python3
"""
Test script for delivery system fixes:
1. <PERSON><PERSON> not found error handling
2. Customer confirmation workflow
"""

import sys
import os
import datetime

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_delivery_personnel_validation():
    """Test delivery personnel Telegram access validation"""
    print("🔍 TESTING DELIVERY PERSONNEL VALIDATION")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import validate_personnel_telegram_access
        
        accessible, inaccessible = validate_personnel_telegram_access()
        
        print(f"✅ Accessible personnel: {len(accessible)}")
        for pid, telegram_id, name in accessible:
            print(f"   - {pid}: {name} (Telegram: {telegram_id})")
        
        print(f"❌ Inaccessible personnel: {len(inaccessible)}")
        for pid, reason in inaccessible:
            print(f"   - {pid}: {reason}")
        
        return len(accessible) > 0
        
    except Exception as e:
        print(f"❌ Error testing personnel validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_confirmation_workflow():
    """Test customer confirmation workflow components"""
    print("\n👤 TESTING CUSTOMER CONFIRMATION WORKFLOW")
    print("=" * 60)
    
    try:
        # Test imports
        from src.bots.order_track_bot import send_customer_confirmation_request
        from src.bot_instance import bot
        from src.handlers.order_handlers import handle_delivery_confirmation
        
        print("✅ All imports successful")
        
        # Test bot connection
        try:
            bot_info = bot.get_me()
            print(f"✅ User bot connected: @{bot_info.username}")
        except Exception as bot_error:
            print(f"❌ User bot connection failed: {bot_error}")
            return False
        
        # Test function signature
        import inspect
        sig = inspect.signature(send_customer_confirmation_request)
        params = list(sig.parameters.keys())
        
        if 'order_number' in params:
            print("✅ send_customer_confirmation_request has correct signature")
        else:
            print(f"❌ Unexpected signature: {params}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing customer confirmation: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_enhanced_error_handling():
    """Test enhanced error handling in payment handlers"""
    print("\n🛡️  TESTING ENHANCED ERROR HANDLING")
    print("=" * 60)
    
    try:
        # Check if enhanced error handling is in place
        with open("src/handlers/payment_handlers.py", "r") as f:
            content = f.read()
        
        # Check for enhanced error handling features
        checks = [
            ("failed_personnel = []", "Failed personnel tracking"),
            ("Invalid Telegram ID", "Telegram ID validation"),
            ("Chat not found", "Chat not found handling"),
            ("Bot blocked", "Bot blocked handling"),
            ("Invalid user ID", "Invalid user ID handling"),
            ("Consider reviewing", "Unreachable personnel suggestion")
        ]
        
        all_checks_passed = True
        for check_text, description in checks:
            if check_text in content:
                print(f"✅ {description}: Found")
            else:
                print(f"❌ {description}: Missing")
                all_checks_passed = False
        
        return all_checks_passed
        
    except Exception as e:
        print(f"❌ Error testing error handling: {e}")
        return False

def test_authorization_fix():
    """Test that authorization fix is in place"""
    print("\n🔐 TESTING AUTHORIZATION FIX")
    print("=" * 60)
    
    try:
        from src.config import DELIVERY_BOT_AUTHORIZED_IDS
        
        print(f"Current authorized IDs: {DELIVERY_BOT_AUTHORIZED_IDS}")
        
        required_ids = [7729984017, 5546595738, 1133538088]
        all_authorized = True
        
        for user_id in required_ids:
            if user_id in DELIVERY_BOT_AUTHORIZED_IDS:
                print(f"✅ {user_id}: Authorized")
            else:
                print(f"❌ {user_id}: NOT authorized")
                all_authorized = False
        
        return all_authorized
        
    except Exception as e:
        print(f"❌ Error testing authorization: {e}")
        return False

def test_order_tracking_bot_connection():
    """Test order tracking bot connection"""
    print("\n📊 TESTING ORDER TRACKING BOT CONNECTION")
    print("=" * 60)
    
    try:
        from src.bots.order_track_bot import order_track_bot
        
        # Test connection
        bot_info = order_track_bot.get_me()
        print(f"✅ Order tracking bot connected: @{bot_info.username}")
        
        # Test notification functions
        from src.bots.order_track_bot import (
            notify_delivery_accepted,
            notify_delivery_completed,
            send_customer_confirmation_request
        )
        
        print("✅ All notification functions imported successfully")
        return True
        
    except Exception as e:
        print(f"❌ Error testing order tracking bot: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 TESTING DELIVERY SYSTEM FIXES")
    print("=" * 80)
    
    # Run all tests
    tests = [
        ("Authorization Fix", test_authorization_fix),
        ("Enhanced Error Handling", test_enhanced_error_handling),
        ("Personnel Validation", test_delivery_personnel_validation),
        ("Customer Confirmation", test_customer_confirmation_workflow),
        ("Order Tracking Bot", test_order_tracking_bot_connection)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"❌ Test {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 TEST RESULTS SUMMARY:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 FIXES IMPLEMENTED:")
        print("1. ✅ Enhanced error handling for 'chat not found' errors")
        print("2. ✅ Telegram ID validation and personnel tracking")
        print("3. ✅ Customer confirmation workflow with better debugging")
        print("4. ✅ Authorization fix for new delivery personnel")
        print("5. ✅ Comprehensive logging for troubleshooting")
        
        print("\n🚀 READY FOR TESTING:")
        print("- Place a test order and approve it")
        print("- Verify delivery broadcast works without chat errors")
        print("- Test complete order workflow with customer confirmation")
        print("- Confirm all three personnel can complete deliveries")
        
        return True
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
