# Manual Data Restoration Guide for Wiz-Aroma

## Problem Summary
The delivery locations have been cleared from Firebase, causing "No valid delivery locations found" errors when users try to place orders from Stadium Area (area_id: 5).

## Solution: Use Maintenance Bot to Restore Data

### Step 1: Start the Maintenance Bot

1. **Run the maintenance bot:**
   ```bash
   python main.py --maintenance
   ```

2. **Access the maintenance bot via Telegram:**
   - Open Telegram and find the maintenance bot
   - Send `/start` to the maintenance bot
   - You should see the main menu with options: Areas, Restaurants, Menus, Delivery Locations & Fees

### Step 2: Add Missing Delivery Locations

1. **Click "Delivery Locations & Fees"**
2. **Click "Add Delivery Location"**
3. **Add each location one by one:**
   - Applied Library
   - Federal Dorm
   - Anfi
   - Central Library
   - Masters Dorm (Lebs Matebiya)
   - B-371 (Fresh)

### Step 3: Add Delivery Fees for Stadium Area

After adding all delivery locations, you need to add delivery fees for Stadium Area (ID: 5):

1. **Click "Add Delivery Fee"**
2. **Select Stadium Area (ID: 5)**
3. **Add fees for each location:**

   - **Applied Library**: 30 birr
   - **Federal Dorm**: 25 birr
   - **Anfi**: 35 birr
   - **Central Library**: 35 birr
   - **Masters Dorm (Lebs Matebiya)**: 40 birr
   - **B-371 (Fresh)**: 45 birr

### Step 4: Add Delivery Fees for Other Areas (Optional but Recommended)

For complete functionality, also add fees for other areas:

#### Bole Area (ID: 1):
- Applied Library: 20 birr
- Federal Dorm: 20 birr
- Anfi: 30 birr
- Central Library: 30 birr
- Masters Dorm (Lebs Matebiya): 35 birr
- B-371 (Fresh): 40 birr

#### Geda Gate Area (ID: 2):
- Central Library: 20 birr
- Anfi: 25 birr
- Applied Library: 30 birr
- Federal Dorm: 35 birr
- Masters Dorm (Lebs Matebiya): 30 birr
- B-371 (Fresh): 35 birr

#### Kereyu Area (ID: 3):
- Applied Library: 25 birr
- Federal Dorm: 20 birr
- Central Library: 40 birr
- Anfi: 40 birr
- Masters Dorm (Lebs Matebiya): 40 birr
- B-371 (Fresh): 45 birr

#### College Mecheresha Area (ID: 4):
- Central Library: 30 birr
- Anfi: 35 birr
- Applied Library: 40 birr
- Federal Dorm: 45 birr
- Masters Dorm (Lebs Matebiya): 30 birr
- B-371 (Fresh): 25 birr

## Alternative: Direct Firebase Console Method

If the maintenance bot is not working, you can add the data directly via Firebase Console:

### 1. Access Firebase Console
- Go to https://console.firebase.google.com
- Select your Wiz-Aroma project
- Navigate to Firestore Database

### 2. Add Delivery Locations Collection
Create a document at path: `delivery_locations`

```json
{
  "delivery_locations": [
    {"id": 1, "name": "Applied Library"},
    {"id": 2, "name": "Federal Dorm"},
    {"id": 3, "name": "Anfi"},
    {"id": 4, "name": "Central Library"},
    {"id": 5, "name": "Masters Dorm (Lebs Matebiya)"},
    {"id": 6, "name": "B-371 (Fresh)"}
  ]
}
```

### 3. Add Delivery Fees Collection
Create a document at path: `delivery_fees`

```json
{
  "delivery_fees": [
    {"area_id": 5, "location_id": 1, "fee": 30},
    {"area_id": 5, "location_id": 2, "fee": 25},
    {"area_id": 5, "location_id": 3, "fee": 35},
    {"area_id": 5, "location_id": 4, "fee": 35},
    {"area_id": 5, "location_id": 5, "fee": 40},
    {"area_id": 5, "location_id": 6, "fee": 45}
  ]
}
```

## Verification Steps

After adding the data:

1. **Test Stadium Area Orders:**
   - Try placing an order from a Stadium Area restaurant
   - Verify that delivery locations appear with correct fees
   - Complete an order to ensure the full flow works

2. **Check Logs:**
   - Monitor the bot logs for any remaining errors
   - Look for successful delivery fee retrievals

3. **Run Test Script:**
   ```bash
   python test_data_consistency_fix.py
   ```

## Expected Results

After completing these steps:

✅ **Stadium Area orders will work** - Users can select delivery locations
✅ **Delivery fees display correctly** - Each location shows the proper fee
✅ **No more "location not found" errors** - All locations exist in Firebase
✅ **Complete order flow works** - From selection to completion

## Troubleshooting

If issues persist:

1. **Check Firebase Connection:**
   - Verify Firebase credentials are correct
   - Ensure internet connection is stable

2. **Verify Data Structure:**
   - Check that area IDs match between collections
   - Ensure location IDs are consistent

3. **Restart Bots:**
   - Stop all bots: `Ctrl+C`
   - Restart with: `python main.py --bot all`

4. **Check Logs:**
   - Look for Firebase connection errors
   - Verify data loading messages

## Contact Information

If you need assistance with the maintenance bot or Firebase console access, please provide:
- Firebase project details
- Maintenance bot access credentials
- Any specific error messages encountered

The key is ensuring that Stadium Area (area_id: 5) has delivery fees configured for all delivery locations in Firebase Firestore.
