#!/usr/bin/env python3
"""
Test script for Availability Status Management System
Tests the enhanced availability status management functionality for delivery personnel.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.utils.delivery_personnel_utils import (
    set_personnel_status,
    toggle_personnel_status,
    get_personnel_status,
    get_personnel_by_status,
    get_status_summary,
    validate_all_statuses,
    update_status_based_on_capacity,
    create_delivery_personnel,
    verify_delivery_personnel,
    get_real_time_capacity
)
from src.data_models import delivery_personnel, delivery_personnel_availability
from src.config import logger

def test_availability_status_management():
    """Test comprehensive availability status management functionality"""
    
    print("🧪 Testing Availability Status Management System")
    print("=" * 60)
    
    # Test 1: Create test personnel
    print("\n1️⃣ Creating test delivery personnel...")
    test_personnel_id = create_delivery_personnel(
        name="Test Driver Alpha",
        phone_number="+251911111111",
        telegram_id="test_alpha_123",
        email="<EMAIL>",
        service_areas=["area_1", "area_2"],
        vehicle_type="motorcycle",
        max_capacity=5
    )
    
    if test_personnel_id:
        print(f"✅ Created test personnel: {test_personnel_id}")
        
        # Verify the personnel
        verify_result = verify_delivery_personnel(test_personnel_id, True)
        print(f"✅ Verified personnel: {verify_result}")
    else:
        print("❌ Failed to create test personnel")
        return False
    
    # Test 2: Test status setting
    print("\n2️⃣ Testing status setting...")
    
    # Test setting to offline
    result = set_personnel_status(test_personnel_id, "offline", "Testing offline status")
    print(f"Set to offline: {result}")
    current_status = get_personnel_status(test_personnel_id)
    print(f"Current status: {current_status}")
    
    # Test setting to available
    result = set_personnel_status(test_personnel_id, "available", "Testing available status")
    print(f"Set to available: {result}")
    current_status = get_personnel_status(test_personnel_id)
    print(f"Current status: {current_status}")
    
    # Test setting to busy
    result = set_personnel_status(test_personnel_id, "busy", "Testing busy status")
    print(f"Set to busy: {result}")
    current_status = get_personnel_status(test_personnel_id)
    print(f"Current status: {current_status}")
    
    # Test 3: Test status toggling
    print("\n3️⃣ Testing status toggling...")
    
    # Toggle from busy to offline
    new_status = toggle_personnel_status(test_personnel_id)
    print(f"Toggled to: {new_status}")
    
    # Toggle from offline to available
    new_status = toggle_personnel_status(test_personnel_id)
    print(f"Toggled to: {new_status}")
    
    # Test 4: Test getting personnel by status
    print("\n4️⃣ Testing personnel filtering by status...")
    
    available_personnel = get_personnel_by_status("available")
    print(f"Available personnel count: {len(available_personnel)}")
    
    busy_personnel = get_personnel_by_status("busy")
    print(f"Busy personnel count: {len(busy_personnel)}")
    
    offline_personnel = get_personnel_by_status("offline")
    print(f"Offline personnel count: {len(offline_personnel)}")
    
    # Test 5: Test status summary
    print("\n5️⃣ Testing status summary...")
    
    summary = get_status_summary()
    print(f"Total personnel: {summary['total_personnel']}")
    print(f"Status counts: {summary['status_counts']}")
    print(f"Verified counts: {summary['verified_counts']}")
    print(f"Last updated: {summary['last_updated']}")
    
    # Test 6: Test capacity-based status updates
    print("\n6️⃣ Testing capacity-based status updates...")
    
    # Set to available first
    set_personnel_status(test_personnel_id, "available", "Preparing for capacity test")
    print(f"Initial status: {get_personnel_status(test_personnel_id)}")
    
    # Test automatic status update based on capacity
    update_result = update_status_based_on_capacity(test_personnel_id)
    print(f"Capacity-based update result: {update_result}")
    print(f"Status after capacity check: {get_personnel_status(test_personnel_id)}")
    
    # Test 7: Test status validation
    print("\n7️⃣ Testing status validation...")
    
    validation_results = validate_all_statuses()
    print(f"Validation results:")
    print(f"  - Total checked: {validation_results['total_checked']}")
    print(f"  - Corrections made: {validation_results['corrections_made']}")
    print(f"  - Mismatches found: {len(validation_results['status_mismatches'])}")
    print(f"  - Validation time: {validation_results['validation_time']}")
    
    if validation_results['corrected_personnel']:
        print("  - Corrected personnel:")
        for correction in validation_results['corrected_personnel']:
            print(f"    • {correction['name']}: {correction['old_status']} → {correction['new_status']}")
    
    # Test 8: Test error handling
    print("\n8️⃣ Testing error handling...")
    
    # Test with invalid personnel ID
    invalid_result = set_personnel_status("invalid_id", "available", "Testing invalid ID")
    print(f"Invalid ID test result: {invalid_result}")
    
    # Test with invalid status
    invalid_status_result = set_personnel_status(test_personnel_id, "invalid_status", "Testing invalid status")
    print(f"Invalid status test result: {invalid_status_result}")
    
    # Test getting status for invalid ID
    invalid_get_result = get_personnel_status("invalid_id")
    print(f"Get status for invalid ID: {invalid_get_result}")
    
    print("\n✅ Availability Status Management System Test Complete!")
    print("=" * 60)
    
    return True

def test_integration_with_existing_systems():
    """Test integration with existing capacity tracking and assignment systems"""
    
    print("\n🔗 Testing Integration with Existing Systems")
    print("=" * 60)
    
    # Check data consistency
    print("\n1️⃣ Checking data consistency...")
    
    total_personnel = len(delivery_personnel)
    total_availability = len(delivery_personnel_availability)
    
    print(f"Personnel records: {total_personnel}")
    print(f"Availability records: {total_availability}")
    print(f"Data consistency: {'✅ Good' if total_personnel == total_availability else '⚠️ Mismatch'}")
    
    # Test status synchronization
    print("\n2️⃣ Testing status synchronization...")
    
    for personnel_id in list(delivery_personnel.keys())[:3]:  # Test first 3 personnel
        personnel_status = delivery_personnel[personnel_id].get('status', 'unknown')
        availability_status = delivery_personnel_availability.get(personnel_id, 'unknown')
        capacity = get_real_time_capacity(personnel_id)
        
        print(f"Personnel {personnel_id[:8]}...")
        print(f"  - Personnel status: {personnel_status}")
        print(f"  - Availability status: {availability_status}")
        print(f"  - Current capacity: {capacity}")
        print(f"  - Synchronized: {'✅' if personnel_status == availability_status else '⚠️'}")
    
    print("\n✅ Integration Test Complete!")
    print("=" * 60)

if __name__ == "__main__":
    try:
        # Run availability status management tests
        test_availability_status_management()
        
        # Run integration tests
        test_integration_with_existing_systems()
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        print(f"\n❌ Test failed: {e}")
        sys.exit(1)
