#!/usr/bin/env python3
"""
Test script to verify confirmed_orders collection functionality.
"""

import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.firebase_db import get_data, set_data, delete_data

def test_confirmed_orders():
    """Test confirmed_orders collection functionality"""
    print("🧪 Testing confirmed_orders collection...")
    
    try:
        # Test reading confirmed orders
        confirmed_orders = get_data("confirmed_orders")
        print(f"✅ Confirmed orders collection accessible")
        
        if confirmed_orders:
            print(f"   Found {len(confirmed_orders)} confirmed orders:")
            for order_number, order_data in confirmed_orders.items():
                status = order_data.get('status', 'Unknown')
                delivery_status = order_data.get('delivery_status', 'Unknown')
                print(f"   Order #{order_number}: Status = {status}, Delivery = {delivery_status}")
        else:
            print("   No confirmed orders found (collection is empty)")
        
        # Test writing to confirmed orders collection
        test_order = {
            "order_number": "TEST_001",
            "user_id": "test_user",
            "status": "CONFIRMED",
            "delivery_status": "pending_assignment",
            "confirmed_at": "2025-06-30 14:30:00",
            "restaurant_id": "1",
            "subtotal": 100,
            "phone_number": "+251963630623",
            "delivery_location": "Test Location"
        }
        
        print("\n🧪 Testing write to confirmed_orders...")
        if set_data("confirmed_orders/TEST_001", test_order):
            print("✅ Successfully wrote test order to confirmed_orders collection")
            
            # Verify the write
            test_read = get_data("confirmed_orders/TEST_001")
            if test_read:
                print("✅ Successfully read back test order")
                print(f"   Order status: {test_read.get('status')}")
                print(f"   Delivery status: {test_read.get('delivery_status')}")
            else:
                print("❌ Failed to read back test order")
            
            # Clean up test order
            if delete_data("confirmed_orders/TEST_001"):
                print("✅ Successfully cleaned up test order")
            else:
                print("⚠️  Failed to clean up test order")
                
            return True
        else:
            print("❌ Failed to write to confirmed_orders collection")
            return False
            
    except Exception as e:
        print(f"❌ Error testing confirmed orders collection: {e}")
        return False

if __name__ == "__main__":
    success = test_confirmed_orders()
    if success:
        print("\n🎉 Confirmed orders collection is working properly!")
    else:
        print("\n❌ Issues found with confirmed orders collection")
        sys.exit(1)
