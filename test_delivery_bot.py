#!/usr/bin/env python3
"""
Test script to verify delivery bot can send messages to delivery personnel
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.config import DELIVERY_BOT_TOKEN
import telebot

def test_delivery_bot():
    """Test if delivery bot can send messages to delivery personnel"""
    print("=== TESTING DELIVERY BOT ===")
    
    # Initialize delivery bot
    try:
        delivery_bot = telebot.TeleBot(DELIVERY_BOT_TOKEN)
        print(f"✅ Delivery bot initialized with token: {DELIVERY_BOT_TOKEN[:20]}...")
        
        # Test delivery personnel Telegram IDs
        test_ids = ["7729984017", "5546595738"]
        
        for telegram_id in test_ids:
            try:
                print(f"📤 Sending test message to {telegram_id}...")
                delivery_bot.send_message(
                    telegram_id,
                    "🧪 **TEST MESSAGE**\n\nThis is a test message from the delivery bot to verify connectivity.\n\nIf you receive this, the delivery bot is working correctly! ✅"
                )
                print(f"✅ Successfully sent test message to {telegram_id}")
            except Exception as e:
                print(f"❌ Failed to send message to {telegram_id}: {e}")
                
    except Exception as e:
        print(f"❌ Failed to initialize delivery bot: {e}")

if __name__ == "__main__":
    test_delivery_bot()
