# Management Bot Setup

This document explains how to set up and configure the Management Bot that provides comprehensive delivery personnel management, analytics, and reporting capabilities.

## Bot Configuration

1. The management bot has been configured with the following details:
   - Bot Token: `7830440989:AAH7Sm9c7pIDflDi-NwtigaQXtImv0ayahg`
   - Chat ID: `7729984017`

2. This bot provides comprehensive management capabilities including delivery personnel management, analytics, and reporting.

## Management Features

The management bot provides the following capabilities:

- **Personnel Management**: Add/remove delivery personnel via Telegram ID
- **Analytics Dashboard**: View daily, weekly, and monthly statistics
- **Performance Tracking**: Monitor delivery assignments and completion rates
- **Real-time Reporting**: Access comprehensive delivery metrics
- **Firebase Integration**: All data stored securely in Firestore

## How to Run

To run the management bot along with the other bots:

```bash
python main.py --bot all
```

To run only the management bot:

```bash
python main.py --bot management
```

## Environment Configuration

The management bot uses two environment variables:

1. `MANAGEMENT_BOT_TOKEN`: The Telegram bot token
2. `MANAGEMENT_CHAT_ID`: The chat ID for management operations

Current configuration:

- Bo<PERSON> Token: `7830440989:AAH7Sm9c7pIDflDi-NwtigaQXtImv0ayahg`
- Chat ID: `7729984017`

## Testing

To verify the management bot is working:

1. Send `/start` command to the management bot
2. Use the inline keyboard to access management features
3. Test personnel management and analytics functions

## Troubleshooting

If you encounter issues with the management bot:

1. Check that the bot token is valid
2. Ensure the authorized user ID (7729984017) has access
3. Verify Firebase credentials are properly configured
4. Check the logs for any error messages related to the management bot
