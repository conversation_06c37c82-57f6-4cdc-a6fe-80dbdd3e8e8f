#!/usr/bin/env python3
"""
Test script to verify Firestore connectivity and data operations.
"""

import os
import sys
import logging

# Add src to path
sys.path.append('src')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_firestore_connection():
    """Test basic Firestore connectivity"""
    try:
        from firebase_db import initialize_firebase, get_data, set_data
        
        print("🔄 Testing Firestore connection...")
        
        # Initialize Firebase
        result = initialize_firebase()
        print(f"✅ Firebase initialized: {result}")
        
        if not result:
            print("❌ Failed to initialize Firebase")
            return False
            
        # Test writing data
        print("🔄 Testing write operation...")
        test_data = {"test": "value", "timestamp": "2025-06-30"}
        write_result = set_data("test_collection/test_doc", test_data)
        print(f"✅ Write operation: {write_result}")
        
        # Test reading data
        print("🔄 Testing read operation...")
        read_data = get_data("test_collection/test_doc")
        print(f"✅ Read operation: {read_data}")
        
        # Test reading collection
        print("🔄 Testing collection read...")
        collection_data = get_data("test_collection")
        print(f"✅ Collection read: {collection_data}")
        
        # Test pending admin reviews
        print("🔄 Testing pending admin reviews...")
        pending_reviews = get_data("pending_admin_reviews")
        print(f"✅ Pending admin reviews: {pending_reviews}")
        print(f"✅ Number of pending orders: {len(pending_reviews) if pending_reviews else 0}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Firestore: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loading():
    """Test data loading from Firestore"""
    try:
        from data_storage import load_user_data
        
        print("🔄 Testing data loading...")
        load_user_data()
        print("✅ Data loading completed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing data loading: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 Starting Firestore connectivity tests...")
    
    # Test 1: Basic Firestore connection
    print("\n" + "="*50)
    print("TEST 1: Firestore Connection")
    print("="*50)
    firestore_ok = test_firestore_connection()
    
    # Test 2: Data loading
    print("\n" + "="*50)
    print("TEST 2: Data Loading")
    print("="*50)
    data_loading_ok = test_data_loading()
    
    # Summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    print(f"Firestore Connection: {'✅ PASS' if firestore_ok else '❌ FAIL'}")
    print(f"Data Loading: {'✅ PASS' if data_loading_ok else '❌ FAIL'}")
    
    if firestore_ok and data_loading_ok:
        print("\n🎉 All tests passed! Firestore is working correctly.")
    else:
        print("\n⚠️ Some tests failed. Please check the errors above.")
