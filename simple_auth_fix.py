#!/usr/bin/env python3
"""
Simple authorization fix for user 5546595738
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def main():
    print("🔧 SIMPLE AUTHORIZATION FIX")
    print("Fixing access for user ID: 5546595738")
    print("=" * 50)
    
    try:
        # Step 1: Import required functions
        print("📦 Importing functions...")
        from src.firebase_db import get_data, set_data
        from src.bots.delivery_bot import clear_authorization_cache, is_authorized
        print("✅ Functions imported successfully")
        
        # Step 2: Check current Firebase data
        print("\n🔍 Checking Firebase data...")
        authorized_personnel = get_data("authorized_delivery_personnel") or {}
        print(f"📊 Found {len(authorized_personnel)} authorized personnel records")
        
        # Step 3: Add user 5546595738 if not exists
        user_id = 5546595738
        personnel_id = f"delivery_personnel_{user_id}"
        
        if personnel_id not in authorized_personnel:
            print(f"\n➕ Adding user {user_id} to authorized personnel...")
            
            auth_record = {
                'telegram_id': user_id,
                'name': 'Delivery Personnel (Legacy)',
                'status': 'active',
                'added_date': '2025-07-10 16:00:00',
                'added_by': 7729984017,
                'source': 'manual_fix'
            }
            
            authorized_personnel[personnel_id] = auth_record
            set_data("authorized_delivery_personnel", authorized_personnel)
            print(f"✅ User {user_id} added to Firebase")
        else:
            print(f"\n✅ User {user_id} already exists in Firebase")
            existing = authorized_personnel[personnel_id]
            print(f"   Status: {existing.get('status', 'unknown')}")
            
            # Ensure status is active
            if existing.get('status') != 'active':
                existing['status'] = 'active'
                authorized_personnel[personnel_id] = existing
                set_data("authorized_delivery_personnel", authorized_personnel)
                print(f"✅ Updated status to active")
        
        # Step 4: Clear cache
        print(f"\n🔄 Clearing authorization cache...")
        clear_authorization_cache()
        print(f"✅ Cache cleared")
        
        # Step 5: Test authorization
        print(f"\n🧪 Testing authorization for user {user_id}...")
        is_auth = is_authorized(user_id)
        
        if is_auth:
            print(f"✅ SUCCESS! User {user_id} is now authorized")
        else:
            print(f"❌ FAILED! User {user_id} is still not authorized")
        
        print(f"\n📋 SUMMARY:")
        print(f"• User {user_id} added to Firebase: ✅")
        print(f"• Authorization cache cleared: ✅")
        print(f"• Authorization test: {'✅' if is_auth else '❌'}")
        
        if is_auth:
            print(f"\n🎉 AUTHORIZATION FIX COMPLETE!")
            print(f"User {user_id} should now have full delivery bot access.")
        else:
            print(f"\n⚠️ Authorization fix may need additional steps.")
            
        return is_auth
        
    except Exception as e:
        print(f"❌ Error during authorization fix: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
