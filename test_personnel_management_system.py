#!/usr/bin/env python3
"""
Comprehensive test script for the Delivery Personnel Management System
Tests all implemented features including personnel management, earnings tracking, and validation.
"""

import sys
import os
import datetime
import json
from typing import Dict, Any

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_data_models():
    """Test the data models and earnings functionality"""
    print("🧪 Testing Data Models...")
    
    try:
        from src.data_models import DeliveryPersonnel, DeliveryPersonnelEarnings
        
        # Test DeliveryPersonnel creation
        personnel = DeliveryPersonnel("test_dp_001")
        personnel.name = "Test Driver"
        personnel.phone_number = "+251912345678"
        personnel.telegram_id = "123456789"
        
        # Test serialization
        personnel_dict = personnel.to_dict()
        assert personnel_dict['name'] == "Test Driver"
        assert personnel_dict['phone_number'] == "+251912345678"
        print("✅ DeliveryPersonnel model works correctly")
        
        # Test DeliveryPersonnelEarnings
        earnings = DeliveryPersonnelEarnings("test_dp_001")
        
        # Test adding earnings
        earnings.add_delivery_earning(25.0)
        assert earnings.daily_earnings == 25.0
        assert earnings.weekly_earnings == 25.0
        assert earnings.completed_deliveries_today == 1
        assert earnings.completed_deliveries_week == 1
        
        # Test serialization
        earnings_dict = earnings.to_dict()
        assert earnings_dict['daily_earnings'] == 25.0
        print("✅ DeliveryPersonnelEarnings model works correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Data models test failed: {e}")
        return False

def test_earnings_utils():
    """Test the earnings utility functions"""
    print("🧪 Testing Earnings Utils...")
    
    try:
        from src.utils.earnings_utils import (
            get_or_create_personnel_earnings,
            get_personnel_earnings_summary,
            get_weekly_earnings_report
        )
        
        # Test earnings creation
        earnings = get_or_create_personnel_earnings("test_dp_002")
        assert earnings is not None
        assert earnings.personnel_id == "test_dp_002"
        print("✅ Earnings creation works correctly")
        
        # Test earnings summary
        summary = get_personnel_earnings_summary("test_dp_002")
        assert isinstance(summary, dict)
        assert 'daily_earnings' in summary
        assert 'weekly_earnings' in summary
        print("✅ Earnings summary works correctly")
        
        # Test weekly report
        report = get_weekly_earnings_report()
        assert isinstance(report, dict)
        assert 'week_start' in report
        assert 'week_end' in report
        print("✅ Weekly earnings report works correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Earnings utils test failed: {e}")
        return False

def test_validation_functions():
    """Test the validation functions"""
    print("🧪 Testing Validation Functions...")
    
    try:
        from src.bots.management_bot import (
            validate_personnel_id,
            validate_phone_number,
            validate_telegram_id,
            validate_name
        )
        
        # Test personnel ID validation
        assert validate_personnel_id("dp_12345678") == True
        assert validate_personnel_id("") == False
        assert validate_personnel_id(None) == False
        print("✅ Personnel ID validation works correctly")
        
        # Test phone number validation
        assert validate_phone_number("+251912345678") == True
        assert validate_phone_number("0912345678") == True
        assert validate_phone_number("123456") == False
        assert validate_phone_number("") == False
        print("✅ Phone number validation works correctly")
        
        # Test Telegram ID validation
        assert validate_telegram_id("123456789") == True
        assert validate_telegram_id("12345") == True
        assert validate_telegram_id("1234") == False
        assert validate_telegram_id("abc123") == False
        print("✅ Telegram ID validation works correctly")
        
        # Test name validation
        assert validate_name("John Doe") == True
        assert validate_name("Mary O'Connor") == True
        assert validate_name("A") == False
        assert validate_name("123") == False
        print("✅ Name validation works correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Validation functions test failed: {e}")
        return False

def test_firebase_integration():
    """Test Firebase integration (mock test)"""
    print("🧪 Testing Firebase Integration...")
    
    try:
        from src.firebase_db import get_data, set_data, update_data
        
        # Test basic Firebase operations (these will use actual Firebase if configured)
        test_data = {"test_key": "test_value", "timestamp": datetime.datetime.now().isoformat()}
        
        # Note: These tests will only work if Firebase is properly configured
        # In a real test environment, you would use Firebase emulator
        print("✅ Firebase integration functions are available")
        
        return True
        
    except Exception as e:
        print(f"❌ Firebase integration test failed: {e}")
        return False

def test_data_storage():
    """Test data storage functions"""
    print("🧪 Testing Data Storage...")
    
    try:
        from src.data_storage import (
            save_delivery_personnel_earnings,
            load_delivery_personnel_earnings_data
        )
        
        # Test earnings data storage
        test_earnings_data = {
            "test_dp_003": {
                "personnel_id": "test_dp_003",
                "daily_earnings": 50.0,
                "weekly_earnings": 200.0,
                "total_lifetime_earnings": 1000.0
            }
        }
        
        # Test save function (will attempt to save to Firebase if configured)
        result = save_delivery_personnel_earnings(test_earnings_data)
        print("✅ Data storage functions are available")
        
        return True
        
    except Exception as e:
        print(f"❌ Data storage test failed: {e}")
        return False

def test_management_bot_functions():
    """Test management bot utility functions"""
    print("🧪 Testing Management Bot Functions...")
    
    try:
        # Test that management bot functions are importable
        from src.bots.management_bot import (
            create_main_menu_keyboard,
            create_personnel_menu_keyboard
        )
        
        # Test keyboard creation
        main_keyboard = create_main_menu_keyboard()
        assert main_keyboard is not None
        print("✅ Main menu keyboard creation works")
        
        personnel_keyboard = create_personnel_menu_keyboard()
        assert personnel_keyboard is not None
        print("✅ Personnel menu keyboard creation works")
        
        return True
        
    except Exception as e:
        print(f"❌ Management bot functions test failed: {e}")
        return False

def run_comprehensive_test():
    """Run all tests and provide summary"""
    print("🚀 Starting Comprehensive Personnel Management System Test")
    print("=" * 60)
    
    tests = [
        ("Data Models", test_data_models),
        ("Earnings Utils", test_earnings_utils),
        ("Validation Functions", test_validation_functions),
        ("Firebase Integration", test_firebase_integration),
        ("Data Storage", test_data_storage),
        ("Management Bot Functions", test_management_bot_functions),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} Test PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} Test FAILED with exception: {e}")
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed / (passed + failed)) * 100:.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! The Personnel Management System is ready for use.")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
