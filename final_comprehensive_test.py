#!/usr/bin/env python3
"""
Final comprehensive test to verify all delivery system fixes are working
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import time
import json
from datetime import datetime

def wait_for_bots_to_start():
    """Wait for bots to fully start"""
    print("⏳ WAITING FOR BOTS TO FULLY START")
    print("=" * 60)
    
    max_wait = 120  # 2 minutes max
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        try:
            with open("logs/bot_2025-07-02.log", "r") as f:
                log_content = f.read()
            
            # Look for the completion message
            if "All bots and data save thread started successfully" in log_content:
                # Check if this is a recent message (within last 5 minutes)
                lines = log_content.strip().split('\n')
                for line in reversed(lines):
                    if "All bots and data save thread started successfully" in line:
                        # Extract timestamp
                        if line.startswith("2025-07-02"):
                            timestamp_str = line.split(" - ")[0]
                            try:
                                log_time = datetime.strptime(timestamp_str, "%Y-%m-%d %H:%M:%S,%f")
                                current_time = datetime.now()
                                time_diff = (current_time - log_time).total_seconds()
                                
                                if time_diff < 300:  # Within last 5 minutes
                                    print(f"✅ Bots are fully started! (started {time_diff:.1f} seconds ago)")
                                    return True
                            except:
                                pass
                        break
            
            print(f"⏳ Still waiting... ({int(time.time() - start_time)}s elapsed)")
            time.sleep(5)
            
        except Exception as e:
            print(f"❌ Error checking logs: {e}")
            time.sleep(5)
    
    print(f"⚠️  Timeout waiting for bots to start")
    return False

def test_delivery_personnel_data():
    """Test delivery personnel data loading"""
    print("\n🔍 TESTING DELIVERY PERSONNEL DATA")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        # Get fresh data from Firebase
        personnel_fb = get_data("delivery_personnel") or {}
        availability_fb = get_data("delivery_personnel_availability") or {}
        capacity_fb = get_data("delivery_personnel_capacity") or {}
        zones_fb = get_data("delivery_personnel_zones") or {}
        
        print(f"📊 Firebase data:")
        print(f"  Personnel: {len(personnel_fb)} records")
        print(f"  Availability: {len(availability_fb)} records")
        print(f"  Capacity: {len(capacity_fb)} records")
        print(f"  Zones: {len(zones_fb)} records")
        
        # Check target personnel
        target_personnel_id = "dp_31fe5be0"
        target_telegram_id = "1133538088"
        
        print(f"\n👤 Checking target personnel {target_personnel_id} ({target_telegram_id}):")
        
        if target_personnel_id in personnel_fb:
            personnel_data = personnel_fb[target_personnel_id]
            print(f"  ✅ Found in Firebase personnel")
            print(f"    Name: {personnel_data.get('name')}")
            print(f"    Telegram ID: {personnel_data.get('telegram_id')}")
            print(f"    Status: {personnel_data.get('status')}")
            print(f"    Verified: {personnel_data.get('is_verified')}")
            print(f"    Service Areas: {personnel_data.get('service_areas')}")
        else:
            print(f"  ❌ NOT found in Firebase personnel")
            return False
        
        if target_personnel_id in availability_fb:
            print(f"  ✅ Found in Firebase availability: {availability_fb[target_personnel_id]}")
        else:
            print(f"  ❌ NOT found in Firebase availability")
            return False
        
        if target_personnel_id in capacity_fb:
            print(f"  ✅ Found in Firebase capacity: {capacity_fb[target_personnel_id]}")
        else:
            print(f"  ❌ NOT found in Firebase capacity")
            return False
        
        if target_personnel_id in zones_fb:
            print(f"  ✅ Found in Firebase zones: {zones_fb[target_personnel_id]}")
        else:
            print(f"  ❌ NOT found in Firebase zones")
            return False
        
        # Test availability function
        print(f"\n📍 Testing find_available_personnel:")
        found_available = False
        
        for area_id in ['1', '2', '3', '4']:
            try:
                available_personnel = find_available_personnel(area_id)
                print(f"  Area {area_id}: {len(available_personnel)} personnel - {available_personnel}")
                
                if target_personnel_id in available_personnel:
                    print(f"    ✅ {target_personnel_id} is available for area {area_id}")
                    found_available = True
                else:
                    print(f"    ❌ {target_personnel_id} is NOT available for area {area_id}")
            except Exception as e:
                print(f"    ❌ Error testing area {area_id}: {e}")
        
        return found_available
        
    except Exception as e:
        print(f"❌ Error testing delivery personnel data: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_order_broadcast():
    """Simulate an order broadcast to test the delivery system"""
    print("\n🚀 SIMULATING ORDER BROADCAST")
    print("=" * 60)
    
    try:
        from src.handlers.payment_handlers import broadcast_order_to_delivery_personnel
        from src.firebase_db import get_data, set_data
        
        # Create a test order
        test_order_number = f"7729984017_{datetime.now().strftime('%y%m%d%H%M')}_TEST"
        
        test_order_data = {
            "order_number": test_order_number,
            "customer_name": "Test Customer",
            "customer_phone": "+1234567890",
            "restaurant_name": "Test Restaurant",
            "delivery_address": "123 Test Street, Test City",
            "order_items": [
                {"name": "Test Item 1", "price": 10.00, "quantity": 1},
                {"name": "Test Item 2", "price": 15.00, "quantity": 2}
            ],
            "subtotal": 40.00,
            "delivery_fee": 5.00,
            "total": 45.00,
            "area_id": "1",
            "status": "pending_assignment",
            "created_at": datetime.now().isoformat()
        }
        
        print(f"📦 Test order created: {test_order_number}")
        print(f"  Customer: {test_order_data['customer_name']}")
        print(f"  Restaurant: {test_order_data['restaurant_name']}")
        print(f"  Area: {test_order_data['area_id']}")
        print(f"  Total: ${test_order_data['total']}")
        
        # Save test order to Firebase
        set_data(f"orders/{test_order_number}", test_order_data)
        print(f"✅ Test order saved to Firebase")
        
        # Broadcast to delivery personnel
        print(f"\n📡 Broadcasting order to delivery personnel...")
        
        success = broadcast_order_to_delivery_personnel(
            test_order_number,
            test_order_data['customer_name'],
            test_order_data['customer_phone'],
            test_order_data['restaurant_name'],
            test_order_data['delivery_address'],
            test_order_data['order_items'],
            test_order_data['subtotal'],
            test_order_data['area_id']
        )
        
        if success:
            print(f"✅ Order broadcast successful!")
            print(f"\n📱 Check delivery bot for order notifications")
            print(f"🎯 Target personnel (1133538088) should receive notification if available")
            return True
        else:
            print(f"❌ Order broadcast failed")
            return False
            
    except Exception as e:
        print(f"❌ Error simulating order broadcast: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_customer_confirmation_fix():
    """Test the customer confirmation workflow fix"""
    print("\n✅ TESTING CUSTOMER CONFIRMATION FIX")
    print("=" * 60)
    
    try:
        # Test the user ID extraction from order number
        test_order_numbers = [
            "7729984017_2507020014_0001",
            "1133538088_2507020015_0002",
            "5546595738_2507020016_0003"
        ]
        
        for order_number in test_order_numbers:
            try:
                # Extract user ID using the same logic as the fix
                customer_user_id = order_number.split('_')[0]
                print(f"  Order: {order_number} → Customer ID: {customer_user_id}")
            except Exception as e:
                print(f"  ❌ Failed to extract user ID from {order_number}: {e}")
                return False
        
        print(f"✅ Customer confirmation fix is working correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error testing customer confirmation fix: {e}")
        return False

if __name__ == "__main__":
    print("🚀 FINAL COMPREHENSIVE TEST")
    print("=" * 80)
    
    # 1. Wait for bots to start
    bots_started = wait_for_bots_to_start()
    
    if not bots_started:
        print(f"\n⚠️  Bots may not be fully started yet, but continuing with tests...")
    
    # 2. Test delivery personnel data
    print(f"\n" + "=" * 80)
    personnel_data_ok = test_delivery_personnel_data()
    
    # 3. Test customer confirmation fix
    print(f"\n" + "=" * 80)
    confirmation_fix_ok = test_customer_confirmation_fix()
    
    # 4. Simulate order broadcast
    print(f"\n" + "=" * 80)
    broadcast_ok = simulate_order_broadcast()
    
    # Summary
    print(f"\n" + "=" * 80)
    print(f"📊 FINAL TEST SUMMARY")
    print(f"=" * 80)
    print(f"  Bots Started: {'✅' if bots_started else '⚠️'}")
    print(f"  Personnel Data: {'✅' if personnel_data_ok else '❌'}")
    print(f"  Confirmation Fix: {'✅' if confirmation_fix_ok else '❌'}")
    print(f"  Order Broadcast: {'✅' if broadcast_ok else '❌'}")
    
    all_tests_passed = personnel_data_ok and confirmation_fix_ok and broadcast_ok
    
    if all_tests_passed:
        print(f"\n🎉 ALL TESTS PASSED! The delivery system is working correctly!")
        print(f"\n🚀 READY FOR REAL ORDER TESTING!")
        print(f"\n💡 Next steps:")
        print(f"  1. Place a real order through the user bot")
        print(f"  2. Approve payment through the finance bot")
        print(f"  3. Verify delivery personnel (including 1133538088) receive notifications")
        print(f"  4. Test complete order lifecycle with customer confirmation")
    else:
        print(f"\n❌ SOME TESTS FAILED - Manual investigation needed")
        
        if not personnel_data_ok:
            print(f"  🔍 Personnel data issue - check Firebase data and bot loading")
        if not confirmation_fix_ok:
            print(f"  🔍 Confirmation fix issue - check order number parsing logic")
        if not broadcast_ok:
            print(f"  🔍 Broadcast issue - check delivery personnel availability logic")
    
    print(f"\n" + "=" * 80)
    print(f"🏁 COMPREHENSIVE TEST COMPLETE")
