#!/usr/bin/env python3
"""
Test script for enhanced name validation in the management bot
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_enhanced_name_validation():
    """Test the enhanced validate_name function"""
    try:
        from bots.management_bot import validate_name
        
        print("🧪 Testing Enhanced Name Validation...")
        print("=" * 60)
        
        # Test cases that should PASS
        valid_names = [
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON>",
            "Driver-01",
            "Driver-02",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON> <PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Van Der Berg",
            "Li Wei",
            "José María",
            "Driver 01",
            "Team Lead-01",
            "<PERSON> Jr.",
            "<PERSON>",
            "<PERSON>",
            "Delivery-Guy-01",
            "Staff-Member-1",
            "<PERSON> III",
            "<PERSON><PERSON><PERSON><PERSON>"
        ]
        
        # Test cases that should FAIL
        invalid_names = [
            "",                    # Empty
            " ",                   # Only space
            "A",                   # Too short
            "123",                 # Only numbers
            "---",                 # Only special chars
            "...",                 # Only periods
            "'''",                 # Only apostrophes
            "   ",                 # Only spaces
            "12345",               # Only numbers
            "-.-",                 # Only special chars
            "A" * 51,              # Too long (51 chars)
            "123456789",           # Only digits
            "!@#$%",               # Invalid special chars
            "<script>",            # Potential security issue
            "SELECT * FROM",       # SQL-like
            "DROP TABLE",          # SQL injection attempt
            None,                  # None value
            123,                   # Non-string
            [],                    # List
        ]
        
        print("✅ Testing VALID names:")
        valid_passed = 0
        for name in valid_names:
            result = validate_name(name)
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"  {status}: '{name}'")
            if result:
                valid_passed += 1
        
        print(f"\n❌ Testing INVALID names:")
        invalid_passed = 0
        for name in invalid_names:
            result = validate_name(name)
            status = "✅ PASS" if not result else "❌ FAIL"
            print(f"  {status}: '{name}' (type: {type(name).__name__})")
            if not result:
                invalid_passed += 1
        
        print("\n" + "=" * 60)
        print("📊 VALIDATION RESULTS:")
        print(f"Valid names passed: {valid_passed}/{len(valid_names)}")
        print(f"Invalid names rejected: {invalid_passed}/{len(invalid_names)}")
        
        total_tests = len(valid_names) + len(invalid_names)
        total_passed = valid_passed + invalid_passed
        success_rate = (total_passed / total_tests) * 100
        
        print(f"Overall success rate: {success_rate:.1f}% ({total_passed}/{total_tests})")
        
        if success_rate >= 95:
            print("\n🎉 EXCELLENT! Enhanced name validation is working correctly!")
            return True
        elif success_rate >= 85:
            print("\n✅ GOOD! Enhanced name validation is mostly working.")
            return True
        else:
            print("\n⚠️ NEEDS IMPROVEMENT! Some validation issues detected.")
            return False
        
    except ImportError as e:
        print(f"❌ Could not import validate_name function: {e}")
        return False
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        return False

def test_edge_cases():
    """Test edge cases for name validation"""
    try:
        from bots.management_bot import validate_name
        
        print("\n🔍 Testing Edge Cases...")
        print("=" * 60)
        
        edge_cases = [
            # Unicode and international names
            ("José", True, "Spanish name with accent"),
            ("François", True, "French name with accent"),
            ("Müller", True, "German name with umlaut"),
            ("李小明", True, "Chinese name"),
            ("محمد", True, "Arabic name"),
            ("Владимир", True, "Russian name"),
            
            # Mixed cases
            ("John-123", True, "Name with number"),
            ("Team-Lead-01", True, "Role with number"),
            ("Driver.01", True, "Name with period"),
            ("O'Connor-Smith", True, "Hyphenated with apostrophe"),
            
            # Boundary cases
            ("Jo", True, "Minimum length (2 chars)"),
            ("A" * 50, True, "Maximum length (50 chars)"),
            ("A" * 51, False, "Over maximum length"),
            
            # Special formatting
            ("  John  ", True, "Name with extra spaces (should be trimmed)"),
            ("John, Jr.", True, "Name with comma"),
            ("Dr. John", True, "Title with period"),
        ]
        
        passed = 0
        for name, expected, description in edge_cases:
            try:
                result = validate_name(name)
                status = "✅ PASS" if result == expected else "❌ FAIL"
                print(f"  {status}: '{name}' -> {result} (expected: {expected}) - {description}")
                if result == expected:
                    passed += 1
            except Exception as e:
                print(f"  ❌ ERROR: '{name}' caused exception: {e}")
        
        print(f"\nEdge cases passed: {passed}/{len(edge_cases)}")
        return passed == len(edge_cases)
        
    except ImportError as e:
        print(f"❌ Could not import validate_name function: {e}")
        return False

def main():
    """Run all name validation tests"""
    print("🚨 ENHANCED NAME VALIDATION TEST SUITE")
    print("Testing the improved validate_name function for delivery personnel")
    print("=" * 80)
    
    # Run tests
    basic_test_passed = test_enhanced_name_validation()
    edge_test_passed = test_edge_cases()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 FINAL TEST RESULTS:")
    print(f"  Basic Validation Tests: {'✅ PASSED' if basic_test_passed else '❌ FAILED'}")
    print(f"  Edge Case Tests: {'✅ PASSED' if edge_test_passed else '❌ FAILED'}")
    
    if basic_test_passed and edge_test_passed:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n✅ Enhanced name validation is working correctly!")
        print("\n📋 IMPROVEMENTS MADE:")
        print("• Now accepts special characters (hyphens, apostrophes, periods, commas)")
        print("• Allows numbers in names (e.g., Driver-01, Team-Lead-02)")
        print("• Supports international characters and Unicode names")
        print("• Maintains security by rejecting malicious input")
        print("• Enforces reasonable length limits (2-50 characters)")
        print("• Requires at least one letter to prevent pure numeric/symbol names")
        
        print("\n🔧 USAGE EXAMPLES:")
        print("✅ John O'Connor - Irish name with apostrophe")
        print("✅ Mary-Jane Smith - Hyphenated first name")
        print("✅ Ahmed Al-Hassan - Arabic name with hyphen")
        print("✅ Driver-01 - Role-based name with number")
        print("✅ Dr. Smith - Title with period")
        print("✅ José María - Spanish name with accents")
        
    else:
        print("\n⚠️ SOME TESTS FAILED!")
        print("Please review the failed tests and fix any issues.")

if __name__ == "__main__":
    main()
