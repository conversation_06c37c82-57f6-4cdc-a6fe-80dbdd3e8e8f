#!/usr/bin/env python3
"""
Test script to verify the Markdown parsing fixes in the management bot.
"""

import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_escape_markdown():
    """Test the escape_markdown function"""
    try:
        from bots.management_bot import escape_markdown
        
        # Test cases with problematic characters
        test_cases = [
            ("Test*Name", "Test\\*Name"),
            ("User_123", "User\\_123"),
            ("Phone: +251-912-345-678", "Phone: \\+251\\-912\\-345\\-678"),
            ("<EMAIL>", "Email@domain\\.com"),
            ("Name with [brackets]", "Name with \\[brackets\\]"),
            ("Code `snippet`", "Code \\`snippet\\`"),
            ("**Bold** text", "\\*\\*Bold\\*\\* text"),
            ("Normal text", "Normal text"),
            ("", ""),
            (None, "N/A"),
        ]
        
        print("🧪 Testing escape_markdown function...")
        
        for input_text, expected in test_cases:
            result = escape_markdown(input_text)
            if result == expected:
                print(f"✅ '{input_text}' -> '{result}'")
            else:
                print(f"❌ '{input_text}' -> '{result}' (expected: '{expected}')")
                return False
        
        print("✅ All escape_markdown tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing escape_markdown: {e}")
        return False

def test_safe_format_message():
    """Test the safe_format_message function"""
    try:
        from bots.management_bot import safe_format_message
        
        # Test safe message formatting
        template = "Hello {name}, your phone is {phone}"
        result = safe_format_message(template, name="John*Doe", phone="+251-123-456")
        
        print("🧪 Testing safe_format_message function...")
        print(f"✅ Template: {template}")
        print(f"✅ Result: {result}")
        
        # Check that special characters are escaped
        if "John\\*Doe" in result and "\\+251\\-123\\-456" in result:
            print("✅ safe_format_message test passed!")
            return True
        else:
            print("❌ safe_format_message test failed!")
            return False
        
    except Exception as e:
        print(f"❌ Error testing safe_format_message: {e}")
        return False

def test_validation_functions():
    """Test the validation functions"""
    try:
        from bots.management_bot import (
            validate_personnel_id,
            validate_phone_number,
            validate_telegram_id,
            validate_name
        )
        
        print("🧪 Testing validation functions...")
        
        # Test validation functions
        tests = [
            (validate_personnel_id, "dp_12345678", True),
            (validate_personnel_id, "", False),
            (validate_phone_number, "+251912345678", True),
            (validate_phone_number, "invalid", False),
            (validate_telegram_id, "123456789", True),
            (validate_telegram_id, "abc", False),
            (validate_name, "John Doe", True),
            (validate_name, "A", False),
        ]
        
        for func, input_val, expected in tests:
            result = func(input_val)
            if result == expected:
                print(f"✅ {func.__name__}('{input_val}') = {result}")
            else:
                print(f"❌ {func.__name__}('{input_val}') = {result} (expected: {expected})")
                return False
        
        print("✅ All validation function tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing validation functions: {e}")
        return False

def test_imports():
    """Test that all necessary modules can be imported"""
    try:
        print("🧪 Testing imports...")
        
        # Test management bot import
        from bots.management_bot import management_bot, show_personnel_menu
        print("✅ Management bot imported successfully")
        
        # Test earnings utils import
        from utils.earnings_utils import get_personnel_earnings_summary
        print("✅ Earnings utils imported successfully")
        
        # Test data models import
        from data_models import DeliveryPersonnelEarnings
        print("✅ Data models imported successfully")
        
        print("✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Markdown Fix Verification Tests")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Escape Markdown", test_escape_markdown),
        ("Safe Format Message", test_safe_format_message),
        ("Validation Functions", test_validation_functions),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n📋 Running {test_name} Test...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} Test PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} Test FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} Test FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! The Markdown parsing fixes are working correctly.")
        print("\n📋 FIXES APPLIED:")
        print("• Added escape_markdown() function to escape special characters")
        print("• Added safe_format_message() function for safe string formatting")
        print("• Updated show_personnel_menu() with error handling and safe formatting")
        print("• Updated personnel view functions with escaped user data")
        print("• Added fallback to plain text if Markdown parsing fails")
        print("• Limited personnel display to prevent message length issues")
        print("\n✅ The management bot should now work without Telegram API parsing errors!")
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
